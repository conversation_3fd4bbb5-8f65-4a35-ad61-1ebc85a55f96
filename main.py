#!/usr/bin/env python3
"""
Main entry point for Binary Trading Bot
"""
import sys
import os
import logging
from datetime import datetime

# Add src directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from config import Config

def setup_logging():
    """Setup logging configuration"""
    logging.basicConfig(
        level=getattr(logging, Config.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(Config.LOG_FILE),
            logging.StreamHandler()
        ]
    )

def test_oanda_connection():
    """Test OANDA API connection"""
    from src.oanda_client import OandaClient
    
    print("Testing OANDA API connection...")
    client = OandaClient()
    
    if client.test_connection():
        print("✅ OANDA API connection successful!")
        
        # Test data fetching
        print("Testing data fetching...")
        data = client.get_candles(count=10)
        if data is not None and len(data) > 0:
            print(f"✅ Successfully fetched {len(data)} candles")
            print(f"Latest candle: {data.iloc[-1]['close']:.5f}")
        else:
            print("❌ Failed to fetch market data")
            return False
            
        # Test current price
        print("Testing current price...")
        price_info = client.get_current_price()
        if price_info:
            print(f"✅ Current price: {price_info['bid']:.5f} / {price_info['ask']:.5f}")
        else:
            print("❌ Failed to fetch current price")
            return False
            
        return True
    else:
        print("❌ OANDA API connection failed!")
        print("Please check your API credentials in config.py")
        return False

def test_strategies():
    """Test all trading strategies"""
    from src.strategies.strategy1_breakout import BreakoutStrategy
    from src.strategies.strategy2_orderblock import OrderBlockStrategy
    from src.strategies.strategy3_pattern import PatternReversalStrategy
    from src.oanda_client import OandaClient
    
    print("\nTesting trading strategies...")
    
    # Get some test data
    client = OandaClient()
    data = client.get_candles(count=200)
    
    if data is None or len(data) < 100:
        print("❌ Insufficient data for strategy testing")
        return False
    
    strategies = {
        'Strategy 1 (Breakout)': BreakoutStrategy(),
        'Strategy 2 (Order Block)': OrderBlockStrategy(),
        'Strategy 3 (Pattern)': PatternReversalStrategy()
    }
    
    for name, strategy in strategies.items():
        try:
            print(f"Testing {name}...")
            signal = strategy.analyze(data)
            if signal:
                print(f"✅ {name} generated signal: {signal['direction']} ({signal['confidence']}%)")
            else:
                print(f"✅ {name} working (no signal generated)")
        except Exception as e:
            print(f"❌ {name} failed: {e}")
            return False
    
    return True

def test_backtesting():
    """Test backtesting functionality"""
    from src.backtesting import BacktestEngine
    
    print("\nTesting backtesting engine...")
    
    backtest_engine = BacktestEngine()
    
    # Load historical data
    try:
        data = backtest_engine.load_historical_data('historical_data_raw.csv')
        if data.empty:
            print("❌ Could not load historical data")
            return False
        
        print(f"✅ Loaded {len(data)} historical candles")
        
        # Run a quick backtest
        print("Running quick backtest...")
        results = backtest_engine.run_backtest(
            data.iloc[-1000:],  # Last 1000 candles
            strategy_name='Strategy1_Breakout'
        )
        
        if 'error' in results:
            print(f"❌ Backtest failed: {results['error']}")
            return False
        
        strategy_results = results.get('Strategy1_Breakout', {})
        performance = strategy_results.get('performance', {})
        
        print(f"✅ Backtest completed:")
        print(f"   - Signals generated: {performance.get('total_signals', 0)}")
        print(f"   - Trades executed: {performance.get('total_trades', 0)}")
        print(f"   - Win rate: {performance.get('win_rate', 0)}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Backtesting failed: {e}")
        return False

def run_tests():
    """Run all system tests"""
    print("🚀 Binary Trading Bot - System Tests")
    print("=" * 50)
    
    setup_logging()
    
    tests = [
        ("OANDA API Connection", test_oanda_connection),
        ("Trading Strategies", test_strategies),
        ("Backtesting Engine", test_backtesting)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The bot is ready to run.")
        print("\nTo start the web interface, run:")
        print("python app.py")
    else:
        print("⚠️  Some tests failed. Please fix the issues before running the bot.")
    
    return passed == total

def main():
    """Main function"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'test':
            run_tests()
        elif command == 'web':
            # Start web interface
            from app import app
            setup_logging()
            print("🌐 Starting web interface...")
            print(f"Open your browser to: http://{Config.FLASK_HOST}:{Config.FLASK_PORT}")
            app.run(
                host=Config.FLASK_HOST,
                port=Config.FLASK_PORT,
                debug=Config.FLASK_DEBUG
            )
        elif command == 'signal':
            # Run signal generation only
            from src.signal_engine import SignalEngine
            setup_logging()
            
            print("📡 Starting signal generation...")
            engine = SignalEngine()
            
            if engine.start_signal_generation():
                print("Signal generation started. Press Ctrl+C to stop.")
                try:
                    while True:
                        import time
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\nStopping signal generation...")
                    engine.stop_signal_generation()
            else:
                print("Failed to start signal generation.")
        else:
            print_usage()
    else:
        print_usage()

def print_usage():
    """Print usage information"""
    print("Binary Trading Bot")
    print("Usage: python main.py [command]")
    print("\nCommands:")
    print("  test    - Run system tests")
    print("  web     - Start web interface")
    print("  signal  - Run signal generation only")
    print("\nExamples:")
    print("  python main.py test")
    print("  python main.py web")

if __name__ == "__main__":
    main()
