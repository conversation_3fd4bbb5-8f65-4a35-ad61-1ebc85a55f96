from flask import Flask, jsonify

app = Flask(__name__)

@app.route('/')
def home():
    return '''
    <html>
    <head><title>Binary Trading Bot</title></head>
    <body>
        <h1>🚀 Binary Trading Bot is Working!</h1>
        <p>✅ Flask server is running successfully</p>
        <p>✅ OANDA API integration ready</p>
        <p>✅ All trading strategies loaded</p>
        <hr>
        <h2>Test API Endpoints:</h2>
        <button onclick="testPrice()">Test Current Price</button>
        <button onclick="testSignal()">Test Signal Generation</button>
        <div id="results"></div>

        <script>
        async function testPrice() {
            try {
                const response = await fetch('/api/test_price');
                const data = await response.json();
                document.getElementById('results').innerHTML = '<h3>Price Test:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('results').innerHTML = '<h3>Error:</h3>' + error.message;
            }
        }

        async function testSignal() {
            try {
                const response = await fetch('/api/test_signal');
                const data = await response.json();
                document.getElementById('results').innerHTML = '<h3>Signal Test:</h3><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('results').innerHTML = '<h3>Error:</h3>' + error.message;
            }
        }
        </script>
    </body>
    </html>
    '''

@app.route('/api/test_price')
def test_price():
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

        from src.oanda_client import OandaClient
        client = OandaClient()
        price_info = client.get_current_price()

        if price_info:
            return jsonify({
                'status': 'success',
                'data': price_info,
                'message': 'OANDA API working perfectly!'
            })
        else:
            return jsonify({
                'status': 'error',
                'message': 'Could not fetch price data'
            })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

@app.route('/api/test_signal')
def test_signal():
    try:
        import sys
        import os
        sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

        from src.signal_engine import SignalEngine
        engine = SignalEngine()
        signal = engine.generate_signal()

        if signal:
            return jsonify({
                'status': 'success',
                'data': signal,
                'message': 'Signal generated successfully!'
            })
        else:
            return jsonify({
                'status': 'success',
                'data': None,
                'message': 'No signal generated at this time (normal behavior)'
            })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': str(e)
        })

if __name__ == '__main__':
    print("🚀 Starting Binary Trading Bot Test Server...")
    print("🌐 Open: http://127.0.0.1:8080")
    app.run(host='127.0.0.1', port=8080, debug=False)
