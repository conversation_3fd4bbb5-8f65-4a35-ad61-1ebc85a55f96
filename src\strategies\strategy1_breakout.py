"""
Strategy 1: Break and Close Beyond Resistance with Volume Confirmation
"""
import pandas as pd
import numpy as np
from typing import Dict, Optional
from .base_strategy import BaseStrategy

class BreakoutStrategy(BaseStrategy):
    """
    Strategy 1: Break and Close Beyond Resistance with Volume Confirmation
    
    Entry Conditions:
    1. Can<PERSON> breaks a resistance level
    2. That candle closes below the resistance level after breaking it
    3. Its volume is higher than the previous candle
    4. The candle does NOT get rejected immediately and closes cleanly with a gap before the next resistance level
    """
    
    def __init__(self):
        super().__init__("Strategy1_Breakout")
        
    def analyze(self, data: pd.DataFrame) -> Optional[Dict]:
        """
        Analyze data for breakout signals
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            Signal dictionary or None
        """
        if len(data) < 50:  # Need sufficient data
            return None
        
        # Get support and resistance levels
        sr_levels = self.calculate_support_resistance(data, window=20)
        
        # Get the last few candles for analysis
        current_candle = data.iloc[-1]
        previous_candle = data.iloc[-2] if len(data) > 1 else None
        
        if previous_candle is None:
            return None
        
        # Check for bullish breakout
        bullish_signal = self._check_bullish_breakout(
            data, current_candle, previous_candle, sr_levels['resistance']
        )
        
        if bullish_signal:
            return bullish_signal
        
        # Check for bearish breakout
        bearish_signal = self._check_bearish_breakout(
            data, current_candle, previous_candle, sr_levels['support']
        )
        
        return bearish_signal
    
    def _check_bullish_breakout(self, data: pd.DataFrame, current_candle: pd.Series, 
                               previous_candle: pd.Series, resistance_levels: list) -> Optional[Dict]:
        """
        Check for bullish breakout conditions
        
        Args:
            data: Full DataFrame
            current_candle: Current candle data
            previous_candle: Previous candle data
            resistance_levels: List of resistance levels
            
        Returns:
            Signal dictionary or None
        """
        if not resistance_levels:
            return None
        
        # Find the nearest resistance level above previous close
        relevant_resistance = None
        for level_info in resistance_levels:
            level = level_info['level']
            if level > previous_candle['close'] and level <= current_candle['high']:
                if relevant_resistance is None or level < relevant_resistance['level']:
                    relevant_resistance = level_info
        
        if relevant_resistance is None:
            return None
        
        resistance_level = relevant_resistance['level']
        
        # Check breakout conditions
        conditions_met = []
        
        # 1. Candle breaks resistance level
        if current_candle['high'] > resistance_level:
            conditions_met.append("Broke resistance level")
        else:
            return None
        
        # 2. Candle closes below resistance after breaking it (for continuation signal)
        if current_candle['close'] < resistance_level:
            conditions_met.append("Closed below resistance after break")
        else:
            return None
        
        # 3. Volume is higher than previous candle
        if current_candle['volume'] > previous_candle['volume']:
            conditions_met.append("Higher volume than previous candle")
            volume_ratio = current_candle['volume'] / previous_candle['volume']
        else:
            return None
        
        # 4. Check for clean close with gap to next resistance
        next_resistance = self._find_next_resistance(resistance_levels, resistance_level)
        gap_to_next = None
        clean_close = True
        
        if next_resistance:
            gap_to_next = next_resistance - current_candle['close']
            # Check if there's sufficient gap (at least 10 pips)
            gap_pips = self.calculate_pips_movement(current_candle['close'], next_resistance)
            if gap_pips < 10:
                clean_close = False
        
        # 5. Check for no immediate rejection
        candle_body = abs(current_candle['close'] - current_candle['open'])
        upper_wick = current_candle['high'] - max(current_candle['open'], current_candle['close'])
        
        # Upper wick should not be more than 2x the body size (indicates rejection)
        if upper_wick > candle_body * 2:
            return None
        else:
            conditions_met.append("No immediate rejection")
        
        if clean_close:
            conditions_met.append("Clean close with gap to next resistance")
        
        # Calculate confidence based on conditions met
        base_confidence = 60
        confidence_boost = 0
        
        # Volume boost
        if volume_ratio > 1.5:
            confidence_boost += 15
        elif volume_ratio > 1.2:
            confidence_boost += 10
        
        # Gap boost
        if clean_close and gap_to_next:
            gap_pips = self.calculate_pips_movement(current_candle['close'], next_resistance)
            if gap_pips > 20:
                confidence_boost += 15
            elif gap_pips > 10:
                confidence_boost += 10
        
        # Resistance strength boost
        if relevant_resistance['touches'] >= 3:
            confidence_boost += 10
        
        final_confidence = min(95, base_confidence + confidence_boost)
        
        return self.format_signal(
            direction="UP",
            confidence=final_confidence,
            current_price=current_candle['close'],
            strategy_name=self.name,
            details={
                'resistance_level': resistance_level,
                'resistance_touches': relevant_resistance['touches'],
                'volume_ratio': round(volume_ratio, 2),
                'gap_to_next_resistance': gap_to_next,
                'conditions_met': conditions_met,
                'candle_type': 'bullish' if current_candle['close'] > current_candle['open'] else 'bearish'
            }
        )
    
    def _check_bearish_breakout(self, data: pd.DataFrame, current_candle: pd.Series, 
                               previous_candle: pd.Series, support_levels: list) -> Optional[Dict]:
        """
        Check for bearish breakout conditions
        
        Args:
            data: Full DataFrame
            current_candle: Current candle data
            previous_candle: Previous candle data
            support_levels: List of support levels
            
        Returns:
            Signal dictionary or None
        """
        if not support_levels:
            return None
        
        # Find the nearest support level below previous close
        relevant_support = None
        for level_info in support_levels:
            level = level_info['level']
            if level < previous_candle['close'] and level >= current_candle['low']:
                if relevant_support is None or level > relevant_support['level']:
                    relevant_support = level_info
        
        if relevant_support is None:
            return None
        
        support_level = relevant_support['level']
        
        # Check breakout conditions
        conditions_met = []
        
        # 1. Candle breaks support level
        if current_candle['low'] < support_level:
            conditions_met.append("Broke support level")
        else:
            return None
        
        # 2. Candle closes above support after breaking it (for continuation signal)
        if current_candle['close'] > support_level:
            conditions_met.append("Closed above support after break")
        else:
            return None
        
        # 3. Volume is higher than previous candle
        if current_candle['volume'] > previous_candle['volume']:
            conditions_met.append("Higher volume than previous candle")
            volume_ratio = current_candle['volume'] / previous_candle['volume']
        else:
            return None
        
        # 4. Check for clean close with gap to next support
        next_support = self._find_next_support(support_levels, support_level)
        gap_to_next = None
        clean_close = True
        
        if next_support:
            gap_to_next = current_candle['close'] - next_support
            gap_pips = self.calculate_pips_movement(next_support, current_candle['close'])
            if gap_pips < 10:
                clean_close = False
        
        # 5. Check for no immediate rejection
        candle_body = abs(current_candle['close'] - current_candle['open'])
        lower_wick = min(current_candle['open'], current_candle['close']) - current_candle['low']
        
        if lower_wick > candle_body * 2:
            return None
        else:
            conditions_met.append("No immediate rejection")
        
        if clean_close:
            conditions_met.append("Clean close with gap to next support")
        
        # Calculate confidence
        base_confidence = 60
        confidence_boost = 0
        
        if volume_ratio > 1.5:
            confidence_boost += 15
        elif volume_ratio > 1.2:
            confidence_boost += 10
        
        if clean_close and gap_to_next:
            gap_pips = self.calculate_pips_movement(next_support, current_candle['close'])
            if gap_pips > 20:
                confidence_boost += 15
            elif gap_pips > 10:
                confidence_boost += 10
        
        if relevant_support['touches'] >= 3:
            confidence_boost += 10
        
        final_confidence = min(95, base_confidence + confidence_boost)
        
        return self.format_signal(
            direction="DOWN",
            confidence=final_confidence,
            current_price=current_candle['close'],
            strategy_name=self.name,
            details={
                'support_level': support_level,
                'support_touches': relevant_support['touches'],
                'volume_ratio': round(volume_ratio, 2),
                'gap_to_next_support': gap_to_next,
                'conditions_met': conditions_met,
                'candle_type': 'bullish' if current_candle['close'] > current_candle['open'] else 'bearish'
            }
        )
    
    def _find_next_resistance(self, resistance_levels: list, current_level: float) -> Optional[float]:
        """Find the next resistance level above current level"""
        next_levels = [level['level'] for level in resistance_levels if level['level'] > current_level]
        return min(next_levels) if next_levels else None
    
    def _find_next_support(self, support_levels: list, current_level: float) -> Optional[float]:
        """Find the next support level below current level"""
        next_levels = [level['level'] for level in support_levels if level['level'] < current_level]
        return max(next_levels) if next_levels else None
