#!/usr/bin/env python3
"""
Test the improved backtesting speed and detailed results
"""
import sys
import os
import time
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_backtest_speed():
    print("🚀 Testing Improved Backtesting...")
    
    start_time = time.time()
    
    from src.backtesting import BacktestEngine
    engine = BacktestEngine()
    
    # Load data
    print("📁 Loading historical data...")
    data = engine.load_historical_data('historical_data_raw.csv')
    print(f"✅ Loaded {len(data)} candles")
    
    # Test with 100 candles (should be fast)
    print("🔄 Running backtest on last 100 candles...")
    results = engine.run_backtest(data.iloc[-100:], 'Strategy1_Breakout')
    
    end_time = time.time()
    duration = end_time - start_time
    
    if 'error' in results:
        print(f"❌ Backtest failed: {results['error']}")
        return
    
    # Extract detailed results
    perf = results['Strategy1_Breakout']['performance']
    breakdown = perf.get('trade_breakdown', {})
    dir_analysis = perf.get('direction_analysis', {})
    conf_analysis = perf.get('confidence_analysis', {})
    
    print(f"\n⚡ Backtest completed in {duration:.2f} seconds")
    print("=" * 50)
    
    # Main Stats
    print("📊 MAIN RESULTS:")
    print(f"   Total Signals: {perf['total_signals']}")
    print(f"   Total Trades: {perf['total_trades']}")
    print(f"   Win Rate: {perf['win_rate']}%")
    print(f"   Profit/Loss: {perf['total_profit_loss']}")
    
    # Detailed Breakdown
    print("\n📈 DETAILED TRADE BREAKDOWN:")
    print(f"   ✅ Wins: {breakdown.get('wins', 0)}")
    print(f"   ❌ Losses: {breakdown.get('losses', 0)}")
    print(f"   Win/Loss Ratio: {breakdown.get('win_loss_ratio', 'N/A')}")
    print(f"   Avg Profit per Trade: {breakdown.get('avg_profit_per_trade', 0)}")
    
    # Direction Analysis
    print("\n🎯 DIRECTION ANALYSIS:")
    print(f"   UP Trades: {dir_analysis.get('up_trades', 0)} (Wins: {dir_analysis.get('up_wins', 0)}, Losses: {dir_analysis.get('up_losses', 0)})")
    print(f"   DOWN Trades: {dir_analysis.get('down_trades', 0)} (Wins: {dir_analysis.get('down_wins', 0)}, Losses: {dir_analysis.get('down_losses', 0)})")
    print(f"   UP Win Rate: {dir_analysis.get('up_win_rate', 0)}%")
    print(f"   DOWN Win Rate: {dir_analysis.get('down_win_rate', 0)}%")
    
    # Confidence Analysis
    print("\n🎯 CONFIDENCE ANALYSIS:")
    high_conf = conf_analysis.get('high_confidence', {})
    medium_conf = conf_analysis.get('medium_confidence', {})
    low_conf = conf_analysis.get('low_confidence', {})
    
    print(f"   High Confidence (80%+): {high_conf.get('count', 0)} trades (Wins: {high_conf.get('wins', 0)}, Losses: {high_conf.get('losses', 0)}) - {high_conf.get('win_rate', 0)}%")
    print(f"   Medium Confidence (60-80%): {medium_conf.get('count', 0)} trades (Wins: {medium_conf.get('wins', 0)}, Losses: {medium_conf.get('losses', 0)}) - {medium_conf.get('win_rate', 0)}%")
    print(f"   Low Confidence (<60%): {low_conf.get('count', 0)} trades (Wins: {low_conf.get('wins', 0)}, Losses: {low_conf.get('losses', 0)}) - {low_conf.get('win_rate', 0)}%")
    
    print("\n" + "=" * 50)
    print("✅ Backtesting is now FAST and provides DETAILED results!")
    print("🌐 Test it on the website: http://127.0.0.1:8080")

if __name__ == "__main__":
    test_backtest_speed()
