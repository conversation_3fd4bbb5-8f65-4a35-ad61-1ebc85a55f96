"""
Backtesting Engine for Binary Trading Bot
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import logging

from .strategies.strategy1_breakout import BreakoutStrategy
from .strategies.strategy2_orderblock import OrderBlockStrategy
from .strategies.strategy3_pattern import PatternReversalStrategy

class BacktestEngine:
    """
    Backtesting engine to test strategies on historical data
    """
    
    def __init__(self):
        self.strategies = {
            'Strategy1_Breakout': BreakoutStrategy(),
            'Strategy2_OrderBlock': OrderBlockStrategy(),
            'Strategy3_Pattern': PatternReversalStrategy()
        }
        self.logger = logging.getLogger(__name__)
    
    def run_backtest(self, data: pd.DataFrame, strategy_name: str = None, 
                    start_date: str = None, end_date: str = None) -> Dict:
        """
        Run backtest on historical data
        
        Args:
            data: Historical OHLCV data
            strategy_name: Specific strategy to test (None for all)
            start_date: Start date for backtest
            end_date: End date for backtest
            
        Returns:
            Backtest results dictionary
        """
        # Filter data by date range if specified
        if start_date or end_date:
            data = self._filter_data_by_date(data, start_date, end_date)
        
        if len(data) < 200:
            return {'error': 'Insufficient data for backtesting'}
        
        # Run backtest for specific strategy or all strategies
        if strategy_name and strategy_name in self.strategies:
            results = {strategy_name: self._backtest_strategy(data, self.strategies[strategy_name])}
        else:
            results = {}
            for name, strategy in self.strategies.items():
                results[name] = self._backtest_strategy(data, strategy)
        
        # Add summary statistics
        results['summary'] = self._calculate_summary_stats(results)
        results['data_info'] = {
            'total_candles': len(data),
            'start_date': str(data.index[0]),
            'end_date': str(data.index[-1]),
            'timeframe': '1M'
        }
        
        return results
    
    def _filter_data_by_date(self, data: pd.DataFrame, start_date: str = None, 
                           end_date: str = None) -> pd.DataFrame:
        """Filter data by date range"""
        filtered_data = data.copy()
        
        if start_date:
            start_dt = pd.to_datetime(start_date)
            filtered_data = filtered_data[filtered_data.index >= start_dt]
        
        if end_date:
            end_dt = pd.to_datetime(end_date)
            filtered_data = filtered_data[filtered_data.index <= end_dt]
        
        return filtered_data
    
    def _backtest_strategy(self, data: pd.DataFrame, strategy) -> Dict:
        """
        Backtest a single strategy
        
        Args:
            data: Historical data
            strategy: Strategy instance
            
        Returns:
            Strategy backtest results
        """
        signals = []
        trades = []
        
        # Minimum data needed for strategy analysis
        min_lookback = 180
        
        # Generate signals
        for i in range(min_lookback, len(data)):
            # Get data up to current point
            current_data = data.iloc[:i+1]
            
            try:
                signal = strategy.analyze(current_data)
                if signal:
                    signal['index'] = i
                    signal['timestamp'] = data.index[i]
                    signals.append(signal)
            except Exception as e:
                self.logger.warning(f"Error generating signal at index {i}: {e}")
        
        # Simulate trades based on signals
        for signal in signals:
            trade_result = self._simulate_trade(data, signal)
            if trade_result:
                trades.append(trade_result)
        
        # Calculate performance metrics
        performance = self._calculate_performance_metrics(trades, signals)
        
        return {
            'signals': signals,
            'trades': trades,
            'performance': performance
        }
    
    def _simulate_trade(self, data: pd.DataFrame, signal: Dict) -> Optional[Dict]:
        """
        Simulate a binary options trade based on signal
        
        Args:
            data: Historical data
            signal: Signal dictionary
            
        Returns:
            Trade result dictionary
        """
        signal_index = signal['index']
        
        # Binary options trade duration (1 minute for next candle)
        if signal_index + 1 >= len(data):
            return None
        
        entry_price = signal['current_price']
        entry_time = signal['timestamp']
        
        # Get next candle for trade result
        next_candle = data.iloc[signal_index + 1]
        exit_price = next_candle['close']
        exit_time = data.index[signal_index + 1]
        
        # Determine trade outcome
        if signal['direction'] == 'UP':
            win = exit_price > entry_price
        else:  # DOWN
            win = exit_price < entry_price
        
        # Calculate profit/loss (binary options typically 70-85% payout)
        payout_rate = 0.8  # 80% payout
        if win:
            profit_loss = payout_rate  # 80% profit
        else:
            profit_loss = -1.0  # 100% loss
        
        return {
            'signal_strategy': signal['strategy'],
            'direction': signal['direction'],
            'confidence': signal['confidence'],
            'entry_time': entry_time,
            'exit_time': exit_time,
            'entry_price': entry_price,
            'exit_price': exit_price,
            'win': win,
            'profit_loss': profit_loss,
            'price_movement_pips': abs(exit_price - entry_price) / 0.0001  # Assuming EUR/USD
        }
    
    def _calculate_performance_metrics(self, trades: List[Dict], signals: List[Dict]) -> Dict:
        """
        Calculate performance metrics for trades
        
        Args:
            trades: List of trade results
            signals: List of generated signals
            
        Returns:
            Performance metrics dictionary
        """
        if not trades:
            return {
                'total_signals': len(signals),
                'total_trades': 0,
                'win_rate': 0,
                'total_profit_loss': 0,
                'average_confidence': 0,
                'best_trade': None,
                'worst_trade': None
            }
        
        total_trades = len(trades)
        winning_trades = [t for t in trades if t['win']]
        losing_trades = [t for t in trades if not t['win']]
        
        win_rate = len(winning_trades) / total_trades * 100
        total_profit_loss = sum(t['profit_loss'] for t in trades)
        average_confidence = sum(s['confidence'] for s in signals) / len(signals) if signals else 0
        
        # Best and worst trades
        best_trade = max(trades, key=lambda x: x['profit_loss']) if trades else None
        worst_trade = min(trades, key=lambda x: x['profit_loss']) if trades else None
        
        # Direction analysis
        up_trades = [t for t in trades if t['direction'] == 'UP']
        down_trades = [t for t in trades if t['direction'] == 'DOWN']
        
        up_win_rate = len([t for t in up_trades if t['win']]) / len(up_trades) * 100 if up_trades else 0
        down_win_rate = len([t for t in down_trades if t['win']]) / len(down_trades) * 100 if down_trades else 0
        
        # Confidence analysis
        high_confidence_trades = [t for t in trades if t['confidence'] >= 80]
        medium_confidence_trades = [t for t in trades if 60 <= t['confidence'] < 80]
        low_confidence_trades = [t for t in trades if t['confidence'] < 60]
        
        high_conf_win_rate = len([t for t in high_confidence_trades if t['win']]) / len(high_confidence_trades) * 100 if high_confidence_trades else 0
        medium_conf_win_rate = len([t for t in medium_confidence_trades if t['win']]) / len(medium_confidence_trades) * 100 if medium_confidence_trades else 0
        low_conf_win_rate = len([t for t in low_confidence_trades if t['win']]) / len(low_confidence_trades) * 100 if low_confidence_trades else 0
        
        return {
            'total_signals': len(signals),
            'total_trades': total_trades,
            'winning_trades': len(winning_trades),
            'losing_trades': len(losing_trades),
            'win_rate': round(win_rate, 2),
            'total_profit_loss': round(total_profit_loss, 2),
            'average_confidence': round(average_confidence, 2),
            'best_trade': best_trade,
            'worst_trade': worst_trade,
            'direction_analysis': {
                'up_trades': len(up_trades),
                'down_trades': len(down_trades),
                'up_win_rate': round(up_win_rate, 2),
                'down_win_rate': round(down_win_rate, 2)
            },
            'confidence_analysis': {
                'high_confidence': {
                    'count': len(high_confidence_trades),
                    'win_rate': round(high_conf_win_rate, 2)
                },
                'medium_confidence': {
                    'count': len(medium_confidence_trades),
                    'win_rate': round(medium_conf_win_rate, 2)
                },
                'low_confidence': {
                    'count': len(low_confidence_trades),
                    'win_rate': round(low_conf_win_rate, 2)
                }
            }
        }
    
    def _calculate_summary_stats(self, results: Dict) -> Dict:
        """Calculate summary statistics across all strategies"""
        if 'summary' in results:
            del results['summary']
        
        if not results:
            return {}
        
        summary = {
            'best_strategy': None,
            'best_win_rate': 0,
            'total_signals_all': 0,
            'total_trades_all': 0,
            'overall_win_rate': 0
        }
        
        all_trades = []
        all_signals = []
        
        for strategy_name, strategy_results in results.items():
            if 'performance' in strategy_results:
                perf = strategy_results['performance']
                
                # Track best strategy by win rate
                if perf['win_rate'] > summary['best_win_rate']:
                    summary['best_win_rate'] = perf['win_rate']
                    summary['best_strategy'] = strategy_name
                
                # Accumulate totals
                summary['total_signals_all'] += perf['total_signals']
                summary['total_trades_all'] += perf['total_trades']
                
                # Collect all trades for overall statistics
                if 'trades' in strategy_results:
                    all_trades.extend(strategy_results['trades'])
                if 'signals' in strategy_results:
                    all_signals.extend(strategy_results['signals'])
        
        # Calculate overall win rate
        if all_trades:
            winning_trades = len([t for t in all_trades if t['win']])
            summary['overall_win_rate'] = round(winning_trades / len(all_trades) * 100, 2)
        
        return summary
    
    def load_historical_data(self, file_path: str) -> pd.DataFrame:
        """
        Load historical data from CSV file
        
        Args:
            file_path: Path to CSV file
            
        Returns:
            DataFrame with historical data
        """
        try:
            data = pd.read_csv(file_path)
            
            # Convert time column to datetime and set as index
            if 'time' in data.columns:
                data['time'] = pd.to_datetime(data['time'])
                data.set_index('time', inplace=True)
            
            # Ensure required columns exist
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in data.columns:
                    raise ValueError(f"Missing required column: {col}")
            
            # Add direction column if not present
            if 'direction' not in data.columns:
                data['direction'] = data.apply(
                    lambda row: 'bullish' if row['close'] > row['open'] 
                    else 'bearish' if row['close'] < row['open'] 
                    else 'neutral', axis=1
                )
            
            self.logger.info(f"Loaded {len(data)} candles from {file_path}")
            return data
            
        except Exception as e:
            self.logger.error(f"Error loading historical data: {e}")
            return pd.DataFrame()
