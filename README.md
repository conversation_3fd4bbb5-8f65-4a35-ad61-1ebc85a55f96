# Binary Trading Bot

A comprehensive binary options trading bot that uses three advanced strategies to analyze live market data from OANDA and generate trading signals with confidence ratings.

## Features

### 🎯 Three Advanced Trading Strategies

1. **Strategy 1: Break and Close Beyond Resistance with Volume Confirmation**
   - Detects breakouts of resistance/support levels
   - Confirms with volume analysis
   - Validates clean closes and gap analysis

2. **Strategy 2: Order Block Rejection with Low Volume (Reversal)**
   - Identifies order blocks in market structure
   - Detects rejection patterns
   - Confirms with volume divergence

3. **Strategy 3: Price Range Movement Reversal (Pattern Recognition)**
   - Analyzes historical movement patterns (5-40 pips)
   - Identifies recurring reversal patterns
   - Validates at support/resistance levels

### 📊 Real-time Features

- **Live Market Data**: Fetches 1-minute candles from OANDA API
- **Signal Timing**: Generates signals 2 seconds before candle close
- **Confidence Scoring**: Each signal includes confidence percentage
- **Multi-Strategy Analysis**: Tests all strategies simultaneously
- **Real-time Dashboard**: Web-based interface with live updates

### 🔍 Backtesting Engine

- Test strategies on historical data
- Comprehensive performance metrics
- Win/loss ratio analysis
- Strategy comparison tools
- Confidence level analysis

### 🌐 Web Interface

- Real-time signal display
- Bot start/stop controls
- Strategy performance monitoring
- Backtesting interface
- Signal history tracking

## Installation

### Prerequisites

- Python 3.8 or higher
- OANDA API account (practice or live)

### Setup

1. **Clone or download the project files**

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Configure API credentials**:
   - Open `config.py`
   - Update your OANDA API credentials:
     ```python
     OANDA_API_KEY = "your-api-key-here"
     OANDA_ACCOUNT_ID = "your-account-id-here"
     ```

4. **Test the installation**:
   ```bash
   python main.py test
   ```

## Usage

### Quick Start

1. **Run system tests**:
   ```bash
   python main.py test
   ```

2. **Start the web interface**:
   ```bash
   python main.py web
   ```

3. **Open your browser** to `http://127.0.0.1:5000`

4. **Click "Start Bot"** to begin signal generation

### Command Line Options

- `python main.py test` - Run all system tests
- `python main.py web` - Start web interface
- `python main.py signal` - Run signal generation only

### Web Interface Guide

#### Dashboard Overview
- **Bot Control Panel**: Start/stop the bot
- **Latest Signal**: Shows the most recent signal with confidence
- **Current Market Data**: Live price and spread information
- **Strategy Performance**: Real-time statistics for each strategy

#### Signal Information
Each signal includes:
- **Direction**: UP or DOWN
- **Confidence**: Percentage (0-100%)
- **Strategy**: Which strategy generated the signal
- **Current Price**: Market price at signal time
- **Details**: Strategy-specific analysis data

#### Backtesting
1. Select strategy (or "All Strategies")
2. Choose date range
3. Click "Run Backtest"
4. View detailed results including:
   - Total signals generated
   - Win/loss ratios
   - Profit/loss analysis
   - Confidence level performance

## Strategy Details

### Strategy 1: Breakout Strategy
**Conditions:**
- Candle breaks resistance/support level
- Closes beyond the level after breaking
- Higher volume than previous candle
- Clean close with gap to next level
- No immediate rejection

**Signal Types:**
- Bullish: Break resistance → close below → continuation UP
- Bearish: Break support → close above → continuation DOWN

### Strategy 2: Order Block Strategy
**Conditions:**
- Price touches/enters order block zone
- Rejection candle forms
- Lower volume than previous candle
- Closes away from order block

**Signal Types:**
- Bullish Reversal: Rejection from bearish order block
- Bearish Reversal: Rejection from bullish order block

### Strategy 3: Pattern Recognition
**Conditions:**
- Analyzes 1-3 hours of historical data
- Finds 2-4 occurrences of similar movements (5-40 pips)
- Current movement matches historical pattern
- Reversal at support/resistance level
- Lower volume confirmation

**Signal Types:**
- Based on recurring pattern analysis
- Validates against historical success rate

## Configuration

### Key Settings (config.py)

```python
# Trading Configuration
INSTRUMENT = "EUR_USD"          # Trading pair
TIMEFRAME = "M1"                # 1-minute candles
SIGNAL_TIMING = 2               # Seconds before candle close
LOOKBACK_PERIODS = 180          # 3 hours of data

# Strategy 3 Configuration
MIN_MOVEMENT_PIPS = 5           # Minimum pattern size
MAX_MOVEMENT_PIPS = 40          # Maximum pattern size
PATTERN_LOOKBACK_HOURS = 3      # Historical analysis period
MIN_PATTERN_OCCURRENCES = 2     # Required pattern repetitions
```

## API Endpoints

The web interface uses these REST API endpoints:

- `GET /api/status` - Bot status and market info
- `POST /api/start_bot` - Start signal generation
- `POST /api/stop_bot` - Stop signal generation
- `GET /api/latest_signal` - Get latest signal
- `GET /api/signal_history` - Get signal history
- `GET /api/strategy_performance` - Get strategy statistics
- `POST /api/backtest` - Run backtesting
- `GET /api/current_price` - Get current market price
- `GET /api/market_data` - Get recent candle data

## File Structure

```
binary-trading-bot/
├── main.py                     # Main entry point
├── app.py                      # Flask web application
├── config.py                   # Configuration settings
├── requirements.txt            # Python dependencies
├── README.md                   # This file
├── src/
│   ├── __init__.py
│   ├── oanda_client.py         # OANDA API client
│   ├── signal_engine.py        # Main signal generation engine
│   ├── backtesting.py          # Backtesting engine
│   └── strategies/
│       ├── __init__.py
│       ├── base_strategy.py    # Base strategy class
│       ├── strategy1_breakout.py
│       ├── strategy2_orderblock.py
│       └── strategy3_pattern.py
├── templates/
│   └── index.html              # Web interface template
├── static/
│   └── js/
│       └── dashboard.js        # Frontend JavaScript
└── historical_data_raw.csv     # Historical data for backtesting
```

## Troubleshooting

### Common Issues

1. **OANDA Connection Failed**
   - Check API credentials in `config.py`
   - Verify internet connection
   - Ensure OANDA account is active

2. **No Signals Generated**
   - Market may be closed (weekends)
   - Insufficient market volatility
   - Strategies may not find valid setups

3. **Backtesting Errors**
   - Ensure `historical_data_raw.csv` exists
   - Check data format and completeness
   - Verify date range parameters

### Logging

Logs are written to:
- Console output
- `trading_bot.log` file

Log levels can be adjusted in `config.py`:
```python
LOG_LEVEL = "INFO"  # DEBUG, INFO, WARNING, ERROR
```

## Disclaimer

This trading bot is for educational and research purposes. Binary options trading involves significant risk. Always:

- Test thoroughly with demo accounts
- Understand the strategies before live trading
- Never risk more than you can afford to lose
- Consider market conditions and volatility
- Use proper risk management

## Support

For issues or questions:
1. Check the troubleshooting section
2. Review log files for error details
3. Ensure all dependencies are installed
4. Verify OANDA API credentials and permissions
