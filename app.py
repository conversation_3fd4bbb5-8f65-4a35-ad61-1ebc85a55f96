"""
Flask Web Application for Binary Trading Bot
"""
from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import logging
import json
from datetime import datetime
import os

from src.signal_engine import SignalEngine
from src.backtesting import BacktestEngine
from config import Config

# Configure logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(Config.LOG_FILE),
        logging.StreamHandler()
    ]
)

app = Flask(__name__)
CORS(app)

# Initialize engines
signal_engine = SignalEngine()
backtest_engine = BacktestEngine()

@app.route('/')
def index():
    """Main dashboard page"""
    return render_template('index.html')

@app.route('/api/start_bot', methods=['POST'])
def start_bot():
    """Start the trading bot"""
    try:
        success = signal_engine.start_signal_generation()
        if success:
            return jsonify({'status': 'success', 'message': 'Bot started successfully'})
        else:
            return jsonify({'status': 'error', 'message': 'Failed to start bot - check OANDA connection'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/stop_bot', methods=['POST'])
def stop_bot():
    """Stop the trading bot"""
    try:
        signal_engine.stop_signal_generation()
        return jsonify({'status': 'success', 'message': 'Bot stopped successfully'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/status')
def get_status():
    """Get bot status"""
    try:
        status = signal_engine.get_status()
        return jsonify(status)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/latest_signal')
def get_latest_signal():
    """Get the latest signal"""
    try:
        signal = signal_engine.get_latest_signal()
        return jsonify(signal)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/signal_history')
def get_signal_history():
    """Get signal history"""
    try:
        limit = request.args.get('limit', 50, type=int)
        history = signal_engine.get_signal_history(limit)
        return jsonify(history)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/strategy_performance')
def get_strategy_performance():
    """Get strategy performance statistics"""
    try:
        performance = signal_engine.get_strategy_performance()
        return jsonify(performance)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/backtest', methods=['POST'])
def run_backtest():
    """Run backtest on historical data"""
    try:
        data = request.get_json()
        
        strategy_name = data.get('strategy')
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        # Load historical data
        historical_data = backtest_engine.load_historical_data('historical_data_raw.csv')
        
        if historical_data.empty:
            return jsonify({'error': 'Could not load historical data'})
        
        # Run backtest
        results = backtest_engine.run_backtest(
            historical_data, 
            strategy_name, 
            start_date, 
            end_date
        )
        
        return jsonify(results)
        
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/current_price')
def get_current_price():
    """Get current market price"""
    try:
        price_info = signal_engine.oanda_client.get_current_price()
        return jsonify(price_info)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/market_data')
def get_market_data():
    """Get recent market data"""
    try:
        count = request.args.get('count', 50, type=int)
        data = signal_engine.oanda_client.get_candles(count=count)
        
        if data is not None:
            # Convert DataFrame to JSON-serializable format
            data_dict = {
                'timestamps': data.index.strftime('%Y-%m-%d %H:%M:%S').tolist(),
                'open': data['open'].tolist(),
                'high': data['high'].tolist(),
                'low': data['low'].tolist(),
                'close': data['close'].tolist(),
                'volume': data['volume'].tolist(),
                'direction': data['direction'].tolist()
            }
            return jsonify(data_dict)
        else:
            return jsonify({'error': 'Could not fetch market data'})
            
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    # Create templates directory if it doesn't exist
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    # Create static directory if it doesn't exist
    if not os.path.exists('static'):
        os.makedirs('static')
        os.makedirs('static/css')
        os.makedirs('static/js')
    
    app.run(
        host=Config.FLASK_HOST,
        port=Config.FLASK_PORT,
        debug=Config.FLASK_DEBUG
    )
