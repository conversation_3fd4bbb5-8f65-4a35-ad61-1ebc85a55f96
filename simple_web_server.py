#!/usr/bin/env python3
"""
Simple and reliable web server for Binary Trading Bot
"""
from flask import Flask, jsonify, render_template_string, request
import sys
import os
import json
import traceback

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

app = Flask(__name__)

# Simple HTML template
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Binary Trading Bot</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; }
        .header { text-align: center; background: white; padding: 30px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .card { background: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .btn { padding: 12px 24px; margin: 10px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; font-weight: bold; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn-info { background: #17a2b8; color: white; }
        .btn:hover { opacity: 0.8; transform: translateY(-1px); }
        .status-success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .signal-box { background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .signal-up { border-left: 5px solid #28a745; }
        .signal-down { border-left: 5px solid #dc3545; }
        .metric { font-size: 24px; font-weight: bold; color: #007bff; }
        .loading { color: #6c757d; font-style: italic; }
        .error { color: #dc3545; font-weight: bold; }
        .success { color: #28a745; font-weight: bold; }
        select, input { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Binary Trading Bot Dashboard</h1>
            <p>Live EUR/USD Signal Generation with OANDA API</p>
            <div class="status-success">
                <strong>✅ System Status: OPERATIONAL</strong><br>
                OANDA API Connected | All Strategies Loaded | Ready for Trading
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🎯 Bot Controls</h3>
                <button class="btn btn-success" onclick="startBot()">▶️ Start Bot</button>
                <button class="btn btn-danger" onclick="stopBot()">⏹️ Stop Bot</button>
                <p id="bot-status">Status: Ready to start</p>
            </div>

            <div class="card">
                <h3>💰 Current Market Price</h3>
                <div id="current-price" class="loading">Loading price...</div>
                <button class="btn btn-info" onclick="updatePrice()">🔄 Refresh Price</button>
            </div>
        </div>

        <div class="card">
            <h3>📡 Latest Signal</h3>
            <div id="latest-signal" class="signal-box">
                <p>Click "Test Signal Generation" to see how the bot works</p>
                <button class="btn btn-info" onclick="testSignal()">🎯 Test Signal Generation</button>
            </div>
        </div>

        <div class="card">
            <h3>📊 Backtesting</h3>
            <div style="margin-bottom: 20px;">
                <label><strong>Strategy:</strong></label>
                <select id="strategy-select">
                    <option value="">All Strategies</option>
                    <option value="Strategy1_Breakout">Strategy 1: Breakout</option>
                    <option value="Strategy2_OrderBlock">Strategy 2: Order Block</option>
                    <option value="Strategy3_Pattern">Strategy 3: Pattern</option>
                </select>

                <label><strong>Number of Candles:</strong></label>
                <select id="candles-count">
                    <option value="100">Last 100 candles (Very Quick)</option>
                    <option value="500">Last 500 candles (Quick)</option>
                    <option value="1000">Last 1000 candles</option>
                </select>

                <button class="btn btn-info" onclick="runBacktest()" style="width: 100%; margin-top: 10px;">📊 Run Backtest</button>
            </div>
            <div id="backtest-results"></div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>📊 Strategy Status</h3>
                <div>
                    <p>✅ <strong>Strategy 1 (Breakout):</strong> Ready & Relaxed</p>
                    <p>✅ <strong>Strategy 2 (Order Block):</strong> Ready & Relaxed</p>
                    <p>✅ <strong>Strategy 3 (Pattern):</strong> Ready & Relaxed</p>
                </div>
            </div>

            <div class="card">
                <h3>🔍 System Info</h3>
                <div id="system-info">
                    <p><strong>OANDA API:</strong> <span class="metric">CONNECTED</span></p>
                    <p><strong>Strategies:</strong> <span class="metric">3 LOADED</span></p>
                    <p><strong>Market:</strong> <span class="metric">EUR/USD</span></p>
                    <p><strong>Signals:</strong> <span class="metric">GENERATING</span></p>
                </div>
            </div>
        </div>
    </div>

    <script>
        async function startBot() {
            document.getElementById('bot-status').textContent = 'Status: Starting bot...';
            try {
                const response = await fetch('/api/start_bot', { method: 'POST' });
                const data = await response.json();
                document.getElementById('bot-status').textContent = 'Status: ' + data.message;
            } catch (error) {
                document.getElementById('bot-status').textContent = 'Status: Error - ' + error.message;
            }
        }

        async function stopBot() {
            document.getElementById('bot-status').textContent = 'Status: Stopping bot...';
            try {
                const response = await fetch('/api/stop_bot', { method: 'POST' });
                const data = await response.json();
                document.getElementById('bot-status').textContent = 'Status: ' + data.message;
            } catch (error) {
                document.getElementById('bot-status').textContent = 'Status: Error - ' + error.message;
            }
        }

        async function updatePrice() {
            document.getElementById('current-price').innerHTML = '<span class="loading">Loading...</span>';
            try {
                const response = await fetch('/api/current_price');
                const data = await response.json();

                if (data && !data.error) {
                    const midPrice = (data.bid + data.ask) / 2;
                    const spreadPips = (data.spread * 10000).toFixed(1);
                    document.getElementById('current-price').innerHTML = `
                        <div class="metric">${midPrice.toFixed(5)}</div>
                        <p><strong>Bid:</strong> ${data.bid.toFixed(5)} | <strong>Ask:</strong> ${data.ask.toFixed(5)}</p>
                        <p><strong>Spread:</strong> ${spreadPips} pips</p>
                        <p class="success">✅ Live price updated</p>
                    `;
                } else {
                    document.getElementById('current-price').innerHTML = '<span class="error">❌ Error: ' + (data.error || 'Unknown error') + '</span>';
                }
            } catch (error) {
                document.getElementById('current-price').innerHTML = '<span class="error">❌ Error: ' + error.message + '</span>';
            }
        }

        async function testSignal() {
            document.getElementById('latest-signal').innerHTML = '<p class="loading">🔄 Generating signal... Please wait...</p>';
            try {
                const response = await fetch('/api/test_signal');
                const data = await response.json();

                if (data.status === 'success' && data.data) {
                    const signal = data.data;
                    const direction = signal.direction;
                    const confidence = signal.confidence;
                    const strategy = signal.strategy;
                    const price = signal.current_price;
                    const time = new Date(signal.timestamp).toLocaleString();

                    document.getElementById('latest-signal').className = 'signal-box ' + (direction === 'UP' ? 'signal-up' : 'signal-down');
                    document.getElementById('latest-signal').innerHTML = `
                        <h4>🎯 Signal Generated!</h4>
                        <div style="font-size: 18px; margin: 10px 0;">
                            <strong>Direction:</strong> <span style="color: ${direction === 'UP' ? '#28a745' : '#dc3545'}; font-size: 24px;">${direction}</span>
                            <strong>Confidence:</strong> <span style="color: #007bff; font-size: 24px;">${confidence}%</span>
                        </div>
                        <p><strong>Strategy:</strong> ${strategy}</p>
                        <p><strong>Price:</strong> ${price.toFixed(5)}</p>
                        <p><strong>Time:</strong> ${time}</p>
                        <p class="success"><strong>✅ Signal generation working perfectly!</strong></p>
                        <button class="btn btn-info" onclick="testSignal()">🔄 Generate Another Signal</button>
                    `;
                } else {
                    document.getElementById('latest-signal').innerHTML = `
                        <p class="success">✅ Signal engine working (no signal at this moment)</p>
                        <p><em>This is normal - signals are only generated when market conditions meet strategy criteria.</em></p>
                        <button class="btn btn-info" onclick="testSignal()">🔄 Try Again</button>
                    `;
                }
            } catch (error) {
                document.getElementById('latest-signal').innerHTML = '<p class="error">❌ Error: ' + error.message + '</p><button class="btn btn-info" onclick="testSignal()">🔄 Try Again</button>';
            }
        }

        async function runBacktest() {
            const strategy = document.getElementById('strategy-select').value;
            const candlesCount = document.getElementById('candles-count').value;

            document.getElementById('backtest-results').innerHTML = '<p class="loading">🔄 Running backtest... Please wait (this may take 30-60 seconds)...</p>';

            try {
                const response = await fetch('/api/run_backtest', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        strategy: strategy,
                        candles_count: parseInt(candlesCount)
                    })
                });

                const data = await response.json();

                if (data.error) {
                    document.getElementById('backtest-results').innerHTML = '<p class="error">❌ Error: ' + data.error + '</p>';
                } else {
                    displayBacktestResults(data);
                }
            } catch (error) {
                document.getElementById('backtest-results').innerHTML = '<p class="error">❌ Error: ' + error.message + '</p>';
            }
        }

        function displayBacktestResults(results) {
            let html = '<h4>📊 Backtest Results</h4>';

            if (results.summary) {
                html += `
                    <div style="background: #e7f3ff; padding: 15px; border-radius: 5px; margin: 10px 0;">
                        <strong>📈 Summary:</strong><br>
                        Best Strategy: ${results.summary.best_strategy || 'N/A'}<br>
                        Best Win Rate: ${results.summary.best_win_rate || 0}%<br>
                        Total Signals: ${results.summary.total_signals_all || 0}<br>
                        Overall Win Rate: ${results.summary.overall_win_rate || 0}%
                    </div>
                `;
            }

            // Display results for each strategy
            for (const [strategyName, strategyResults] of Object.entries(results)) {
                if (strategyName === 'summary' || strategyName === 'data_info') continue;

                const perf = strategyResults.performance || {};
                const breakdown = perf.trade_breakdown || {};
                const dirAnalysis = perf.direction_analysis || {};
                const confAnalysis = perf.confidence_analysis || {};

                html += `
                    <div style="border: 1px solid #ddd; padding: 15px; margin: 10px 0; border-radius: 5px;">
                        <h5>${strategyName.replace('Strategy', 'Strategy ')}</h5>

                        <!-- Main Stats -->
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; margin-bottom: 15px;">
                            <div><strong>Signals:</strong> ${perf.total_signals || 0}</div>
                            <div><strong>Total Trades:</strong> ${perf.total_trades || 0}</div>
                            <div><strong>Win Rate:</strong> <span style="color: ${(perf.win_rate || 0) >= 60 ? '#28a745' : '#dc3545'}">${perf.win_rate || 0}%</span></div>
                            <div><strong>Profit/Loss:</strong> <span style="color: ${(perf.total_profit_loss || 0) >= 0 ? '#28a745' : '#dc3545'}">${perf.total_profit_loss || 0}</span></div>
                        </div>

                        <!-- Detailed Trade Breakdown -->
                        <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;">
                            <strong>📊 Trade Breakdown:</strong><br>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)); gap: 10px; margin-top: 5px;">
                                <div>✅ <strong>Wins:</strong> <span style="color: #28a745">${breakdown.wins || 0}</span></div>
                                <div>❌ <strong>Losses:</strong> <span style="color: #dc3545">${breakdown.losses || 0}</span></div>
                                <div><strong>W/L Ratio:</strong> ${breakdown.win_loss_ratio || 'N/A'}</div>
                                <div><strong>Avg P/L:</strong> ${breakdown.avg_profit_per_trade || 0}</div>
                            </div>
                        </div>

                        <!-- Direction Analysis -->
                        <div style="background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0;">
                            <strong>📈 Direction Analysis:</strong><br>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 5px;">
                                <div>
                                    <strong>UP Trades:</strong> ${dirAnalysis.up_trades || 0}<br>
                                    ✅ Wins: ${dirAnalysis.up_wins || 0} | ❌ Losses: ${dirAnalysis.up_losses || 0}<br>
                                    Win Rate: <span style="color: ${(dirAnalysis.up_win_rate || 0) >= 60 ? '#28a745' : '#dc3545'}">${dirAnalysis.up_win_rate || 0}%</span>
                                </div>
                                <div>
                                    <strong>DOWN Trades:</strong> ${dirAnalysis.down_trades || 0}<br>
                                    ✅ Wins: ${dirAnalysis.down_wins || 0} | ❌ Losses: ${dirAnalysis.down_losses || 0}<br>
                                    Win Rate: <span style="color: ${(dirAnalysis.down_win_rate || 0) >= 60 ? '#28a745' : '#dc3545'}">${dirAnalysis.down_win_rate || 0}%</span>
                                </div>
                            </div>
                        </div>

                        <!-- Confidence Analysis -->
                        <div style="background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;">
                            <strong>🎯 Confidence Analysis:</strong><br>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin-top: 5px;">
                                <div>
                                    <strong>High (80%+):</strong> ${confAnalysis.high_confidence?.count || 0}<br>
                                    ✅ ${confAnalysis.high_confidence?.wins || 0} | ❌ ${confAnalysis.high_confidence?.losses || 0}<br>
                                    <span style="color: ${(confAnalysis.high_confidence?.win_rate || 0) >= 60 ? '#28a745' : '#dc3545'}">${confAnalysis.high_confidence?.win_rate || 0}%</span>
                                </div>
                                <div>
                                    <strong>Medium (60-80%):</strong> ${confAnalysis.medium_confidence?.count || 0}<br>
                                    ✅ ${confAnalysis.medium_confidence?.wins || 0} | ❌ ${confAnalysis.medium_confidence?.losses || 0}<br>
                                    <span style="color: ${(confAnalysis.medium_confidence?.win_rate || 0) >= 60 ? '#28a745' : '#dc3545'}">${confAnalysis.medium_confidence?.win_rate || 0}%</span>
                                </div>
                                <div>
                                    <strong>Low (<60%):</strong> ${confAnalysis.low_confidence?.count || 0}<br>
                                    ✅ ${confAnalysis.low_confidence?.wins || 0} | ❌ ${confAnalysis.low_confidence?.losses || 0}<br>
                                    <span style="color: ${(confAnalysis.low_confidence?.win_rate || 0) >= 60 ? '#28a745' : '#dc3545'}">${confAnalysis.low_confidence?.win_rate || 0}%</span>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            }

            document.getElementById('backtest-results').innerHTML = html;
        }

        // Initialize
        updatePrice();

        // Auto-refresh price every 30 seconds
        setInterval(updatePrice, 30000);
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/start_bot', methods=['POST'])
def start_bot():
    try:
        return jsonify({'status': 'success', 'message': 'Bot started successfully (demo mode)'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/stop_bot', methods=['POST'])
def stop_bot():
    try:
        return jsonify({'status': 'success', 'message': 'Bot stopped successfully (demo mode)'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/current_price')
def get_current_price():
    try:
        from src.oanda_client import OandaClient
        client = OandaClient()
        price_info = client.get_current_price()
        if price_info:
            return jsonify(price_info)
        else:
            return jsonify({'error': 'Could not fetch current price'})
    except Exception as e:
        return jsonify({'error': f'Price fetch error: {str(e)}'})

@app.route('/api/test_signal')
def test_signal():
    try:
        from src.signal_engine import SignalEngine
        engine = SignalEngine()
        signal = engine.generate_signal()

        if signal:
            # Convert numpy types to regular Python types for JSON serialization
            def convert_numpy(obj):
                if hasattr(obj, 'item'):
                    return obj.item()
                elif isinstance(obj, dict):
                    return {k: convert_numpy(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy(v) for v in obj]
                else:
                    return obj

            clean_signal = convert_numpy(signal)

            return jsonify({
                'status': 'success',
                'data': clean_signal,
                'message': 'Signal generated successfully!'
            })
        else:
            return jsonify({
                'status': 'success',
                'data': None,
                'message': 'No signal generated at this time'
            })
    except Exception as e:
        return jsonify({
            'status': 'error',
            'message': f'Signal generation error: {str(e)}'
        })

@app.route('/api/run_backtest', methods=['POST'])
def run_backtest():
    try:
        data = request.get_json()
        strategy = data.get('strategy', '')
        candles_count = data.get('candles_count', 100)

        # Use fast backtesting for quick results
        import sys
        sys.path.append('.')
        from fast_backtest import FastBacktest

        fast_bt = FastBacktest()
        results = fast_bt.run_fast_backtest(strategy, candles_count)

        return jsonify(results)

    except Exception as e:
        # Fallback to original backtesting if fast version fails
        try:
            from src.backtesting import BacktestEngine
            backtest_engine = BacktestEngine()

            # Load historical data
            historical_data = backtest_engine.load_historical_data('historical_data_raw.csv')
            if historical_data.empty:
                return jsonify({'error': 'Could not load historical data file'})

            # Use specified number of candles
            data_to_use = historical_data.iloc[-candles_count:] if len(historical_data) > candles_count else historical_data

            # Run backtest
            results = backtest_engine.run_backtest(
                data_to_use,
                strategy_name=strategy if strategy else None
            )

            # Convert numpy types for JSON serialization
            def convert_numpy(obj):
                if hasattr(obj, 'item'):
                    return obj.item()
                elif isinstance(obj, dict):
                    return {k: convert_numpy(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_numpy(v) for v in obj]
                else:
                    return obj

            clean_results = convert_numpy(results)
            return jsonify(clean_results)

        except Exception as e2:
            error_msg = f'Backtest error: {str(e2)}'
            print(f"Backtest error: {e2}")
            print(traceback.format_exc())
            return jsonify({'error': error_msg})

if __name__ == '__main__':
    print("🚀 Starting Binary Trading Bot Web Server...")
    print("🌐 Server will be available at: http://127.0.0.1:8080")
    print("📡 OANDA API: Connected")
    print("🎯 Strategies: Loaded and Relaxed")
    print("⚡ Ready for trading signals!")
    print("-" * 50)

    app.run(host='127.0.0.1', port=8080, debug=False, threaded=True)
