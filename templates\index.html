<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Binary Trading Bot Dashboard</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        .signal-card {
            border-left: 5px solid #007bff;
            transition: all 0.3s ease;
        }
        .signal-card.up {
            border-left-color: #28a745;
        }
        .signal-card.down {
            border-left-color: #dc3545;
        }
        .confidence-bar {
            height: 20px;
            background: linear-gradient(90deg, #dc3545 0%, #ffc107 50%, #28a745 100%);
            border-radius: 10px;
            position: relative;
        }
        .confidence-indicator {
            position: absolute;
            top: 0;
            height: 100%;
            width: 4px;
            background: #000;
            border-radius: 2px;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-running { background-color: #28a745; }
        .status-stopped { background-color: #dc3545; }
        .status-warning { background-color: #ffc107; }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .strategy-card {
            border: 1px solid #e0e0e0;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 15px;
            transition: transform 0.2s;
        }
        
        .strategy-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line"></i> Binary Trading Bot
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text" id="status-indicator">
                    <span class="status-indicator status-stopped"></span>
                    <span id="status-text">Stopped</span>
                </span>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Control Panel -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> Bot Control Panel</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <button id="start-bot" class="btn btn-success btn-lg me-2">
                                    <i class="fas fa-play"></i> Start Bot
                                </button>
                                <button id="stop-bot" class="btn btn-danger btn-lg">
                                    <i class="fas fa-stop"></i> Stop Bot
                                </button>
                            </div>
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <span class="me-3">Market Status:</span>
                                    <span id="market-status" class="badge bg-secondary">Unknown</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Latest Signal -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card signal-card" id="latest-signal-card">
                    <div class="card-header">
                        <h5><i class="fas fa-signal"></i> Latest Signal</h5>
                    </div>
                    <div class="card-body" id="latest-signal-content">
                        <p class="text-muted">No signals generated yet. Start the bot to begin signal generation.</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Market Data -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="metric-card">
                    <h6><i class="fas fa-dollar-sign"></i> Current Price</h6>
                    <h3 id="current-price">--</h3>
                    <small>Bid: <span id="bid-price">--</span> | Ask: <span id="ask-price">--</span></small>
                </div>
            </div>
            <div class="col-md-6">
                <div class="metric-card">
                    <h6><i class="fas fa-chart-bar"></i> Spread</h6>
                    <h3 id="spread">--</h3>
                    <small>Pips</small>
                </div>
            </div>
        </div>

        <!-- Strategy Performance -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-trophy"></i> Strategy Performance</h5>
                    </div>
                    <div class="card-body" id="strategy-performance">
                        <div class="row" id="strategy-cards">
                            <!-- Strategy cards will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backtesting Section -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-history"></i> Backtesting</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label for="strategy-select" class="form-label">Strategy</label>
                                <select class="form-select" id="strategy-select">
                                    <option value="">All Strategies</option>
                                    <option value="Strategy1_Breakout">Strategy 1: Breakout</option>
                                    <option value="Strategy2_OrderBlock">Strategy 2: Order Block</option>
                                    <option value="Strategy3_Pattern">Strategy 3: Pattern</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="start-date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="start-date">
                            </div>
                            <div class="col-md-3">
                                <label for="end-date" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="end-date">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <button id="run-backtest" class="btn btn-primary d-block">
                                    <i class="fas fa-play"></i> Run Backtest
                                </button>
                            </div>
                        </div>
                        <div id="backtest-results" class="mt-4" style="display: none;">
                            <!-- Backtest results will be displayed here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Signal History -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Signal History</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped" id="signal-history-table">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Strategy</th>
                                        <th>Direction</th>
                                        <th>Confidence</th>
                                        <th>Price</th>
                                        <th>Details</th>
                                    </tr>
                                </thead>
                                <tbody id="signal-history-tbody">
                                    <!-- Signal history will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/dashboard.js"></script>
</body>
</html>
