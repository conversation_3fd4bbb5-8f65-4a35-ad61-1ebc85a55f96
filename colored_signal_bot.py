#!/usr/bin/env python3
"""
Colored Binary Trading Bot with Table Format
"""
import sys
import os
import time
from datetime import datetime

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# ANSI Color codes for Windows/Linux
class Colors:
    # Text colors
    BLUE = '\033[94m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    MAGENTA = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    GRAY = '\033[90m'
    
    # Background colors
    BG_WHITE = '\033[107m'
    BG_BLACK = '\033[40m'
    
    # Styles
    BOLD = '\033[1m'
    RESET = '\033[0m'

class ColoredSignalBot:
    def __init__(self):
        # Enable ANSI colors on Windows
        if os.name == 'nt':
            os.system('color')
        
        self.setup_signal_engine()
        self.print_header()
    
    def setup_signal_engine(self):
        """Initialize the signal engine"""
        try:
            from src.signal_engine import SignalEngine
            from src.oanda_client import OandaClient
            
            self.signal_engine = SignalEngine()
            self.oanda_client = OandaClient()
            print(f"{Colors.GREEN}✅ OANDA API Connected Successfully{Colors.RESET}")
            print(f"{Colors.GREEN}✅ All 3 Strategies Loaded{Colors.RESET}")
            print(f"{Colors.CYAN}📡 Monitoring EUR/USD on 1-minute candles{Colors.RESET}")
            print()
        except Exception as e:
            print(f"{Colors.RED}❌ Error initializing bot: {e}{Colors.RESET}")
            sys.exit(1)
    
    def print_header(self):
        """Print the formatted table header"""
        print(f"{Colors.YELLOW}{'='*130}{Colors.RESET}")
        print(f"{Colors.YELLOW}{Colors.BOLD}🚀 BINARY TRADING BOT - LIVE SIGNAL GENERATION{Colors.RESET}")
        print(f"{Colors.YELLOW}{'='*130}{Colors.RESET}")
        print()
        
        # Table header with colors
        header = (
            f"{Colors.BLUE}{Colors.BG_WHITE}{'DATE':<12}{Colors.RESET} "
            f"{Colors.MAGENTA}{Colors.BG_WHITE}{'TIME':<8}{Colors.RESET} "
            f"{Colors.GREEN}{Colors.BG_WHITE}{'DIRECTION':<10}{Colors.RESET} "
            f"{Colors.CYAN}{Colors.BG_WHITE}{'CONFIDENCE':<12}{Colors.RESET} "
            f"{Colors.YELLOW}{Colors.BG_WHITE}{'STRATEGY':<20}{Colors.RESET} "
            f"{Colors.RED}{Colors.BG_WHITE}{'MARKET PRICE':<13}{Colors.RESET} "
            f"{Colors.WHITE}{Colors.BG_BLACK}{'STATUS':<25}{Colors.RESET}"
        )
        print(header)
        print(f"{Colors.WHITE}{'-'*130}{Colors.RESET}")
    
    def format_signal_output(self, signal_data, market_price):
        """Format signal output in table format with colors"""
        now = datetime.now()
        date_str = now.strftime("%Y-%m-%d")
        time_str = now.strftime("%H:%M:%S")
        
        if signal_data:
            # Signal found - format with colors
            direction = signal_data['direction']
            confidence = f"{signal_data['confidence']}%"
            strategy = signal_data['strategy'].replace('Strategy', 'S').replace('_', '-')
            
            # Color direction based on UP/DOWN
            if direction == "UP":
                direction_colored = f"{Colors.GREEN}{direction:<10}{Colors.RESET}"
            else:
                direction_colored = f"{Colors.RED}{direction:<10}{Colors.RESET}"
            
            # Color confidence based on level
            conf_value = signal_data['confidence']
            if conf_value >= 90:
                confidence_colored = f"{Colors.GREEN}{Colors.BOLD}{confidence:<12}{Colors.RESET}"
            elif conf_value >= 80:
                confidence_colored = f"{Colors.YELLOW}{confidence:<12}{Colors.RESET}"
            else:
                confidence_colored = f"{Colors.CYAN}{confidence:<12}{Colors.RESET}"
            
            output = (
                f"{Colors.BLUE}{date_str:<12}{Colors.RESET} "
                f"{Colors.MAGENTA}{time_str:<8}{Colors.RESET} "
                f"{direction_colored} "
                f"{confidence_colored} "
                f"{Colors.YELLOW}{strategy:<20}{Colors.RESET} "
                f"{Colors.RED}{market_price:<13.5f}{Colors.RESET} "
                f"{Colors.GREEN}{Colors.BOLD}{'🎯 SIGNAL GENERATED':<25}{Colors.RESET}"
            )
        else:
            # No signal found
            output = (
                f"{Colors.BLUE}{date_str:<12}{Colors.RESET} "
                f"{Colors.MAGENTA}{time_str:<8}{Colors.RESET} "
                f"{Colors.GRAY}{'---':<10}{Colors.RESET} "
                f"{Colors.GRAY}{'---':<12}{Colors.RESET} "
                f"{Colors.GRAY}{'---':<20}{Colors.RESET} "
                f"{Colors.RED}{market_price:<13.5f}{Colors.RESET} "
                f"{Colors.GRAY}{'❌ NO SIGNAL FOUND':<25}{Colors.RESET}"
            )
        
        print(output)
    
    def get_current_market_price(self):
        """Get current EUR/USD market price"""
        try:
            price_data = self.oanda_client.get_current_price()
            if price_data:
                return (price_data['bid'] + price_data['ask']) / 2
            else:
                return 0.0
        except Exception as e:
            print(f"{Colors.RED}❌ Error fetching price: {e}{Colors.RESET}")
            return 0.0
    
    def wait_for_signal_time(self):
        """Wait until 58 seconds of the minute to fetch data"""
        now = datetime.now()
        current_second = now.second
        
        if current_second < 58:
            wait_time = 58 - current_second
            print(f"{Colors.CYAN}⏳ Waiting {wait_time} seconds for next signal check (fetching at {now.strftime('%H:%M')}:58)...{Colors.RESET}", end='\r')
            time.sleep(wait_time)
        elif current_second >= 58:
            wait_time = (60 - current_second) + 58
            next_minute = now.replace(second=0, microsecond=0)
            next_minute = next_minute.replace(minute=next_minute.minute + 1)
            print(f"{Colors.CYAN}⏳ Waiting {wait_time} seconds for next signal check (fetching at {next_minute.strftime('%H:%M')}:58)...{Colors.RESET}", end='\r')
            time.sleep(wait_time)
    
    def run_signal_generation(self):
        """Main signal generation loop"""
        print(f"{Colors.GREEN}{Colors.BOLD}🚀 Starting live signal generation...{Colors.RESET}")
        print(f"{Colors.CYAN}📊 Fetching market data at XX:XX:58 every minute{Colors.RESET}")
        print(f"{Colors.YELLOW}⚡ Press Ctrl+C to stop{Colors.RESET}")
        print()
        
        try:
            while True:
                # Wait for the right time (58 seconds)
                self.wait_for_signal_time()
                
                # Clear the waiting message
                print(" " * 80, end='\r')
                
                # Get current market price
                market_price = self.get_current_market_price()
                
                # Generate signal
                try:
                    signal = self.signal_engine.generate_signal()
                    self.format_signal_output(signal, market_price)
                except Exception as e:
                    self.format_signal_output(None, market_price)
                
                # Wait a bit before next check
                time.sleep(2)
                
        except KeyboardInterrupt:
            print(f"\n{Colors.YELLOW}⏹️ Signal generation stopped by user{Colors.RESET}")
            print(f"{Colors.GREEN}✅ Bot shutdown complete{Colors.RESET}")

if __name__ == "__main__":
    bot = ColoredSignalBot()
    bot.run_signal_generation()
