#!/usr/bin/env python3
"""
Binary Trading Bot with Formatted Signal Output
"""
import sys
import os
import time
from datetime import datetime
from colorama import init, Fore, Back, Style

# Initialize colorama for Windows color support
init(autoreset=True)

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class FormattedSignalBot:
    def __init__(self):
        self.setup_signal_engine()
        self.print_header()
    
    def setup_signal_engine(self):
        """Initialize the signal engine"""
        try:
            from src.signal_engine import SignalEngine
            from src.oanda_client import OandaClient
            
            self.signal_engine = SignalEngine()
            self.oanda_client = OandaClient()
            print(f"{Fore.GREEN}✅ OANDA API Connected Successfully{Style.RESET_ALL}")
            print(f"{Fore.GREEN}✅ All 3 Strategies Loaded{Style.RESET_ALL}")
            print(f"{Fore.CYAN}📡 Monitoring EUR/USD on 1-minute candles{Style.RESET_ALL}")
            print()
        except Exception as e:
            print(f"{Fore.RED}❌ Error initializing bot: {e}{Style.RESET_ALL}")
            sys.exit(1)
    
    def print_header(self):
        """Print the formatted table header"""
        print(f"{Fore.YELLOW}{'='*120}{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}🚀 BINARY TRADING BOT - LIVE SIGNAL GENERATION{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}{'='*120}{Style.RESET_ALL}")
        print()
        
        # Table header with colors and proper spacing
        header = (
            f"{Fore.BLUE}{Back.WHITE}{'DATE':<12}{Style.RESET_ALL} "
            f"{Fore.MAGENTA}{Back.WHITE}{'TIME':<8}{Style.RESET_ALL} "
            f"{Fore.GREEN}{Back.WHITE}{'DIRECTION':<10}{Style.RESET_ALL} "
            f"{Fore.CYAN}{Back.WHITE}{'CONFIDENCE':<12}{Style.RESET_ALL} "
            f"{Fore.YELLOW}{Back.WHITE}{'STRATEGY':<20}{Style.RESET_ALL} "
            f"{Fore.RED}{Back.WHITE}{'MARKET PRICE':<13}{Style.RESET_ALL} "
            f"{Fore.WHITE}{Back.BLACK}{'STATUS':<25}{Style.RESET_ALL}"
        )
        print(header)
        print(f"{Fore.WHITE}{'-'*120}{Style.RESET_ALL}")
    
    def format_signal_output(self, signal_data, market_price):
        """Format signal output in table format with colors"""
        now = datetime.now()
        date_str = now.strftime("%Y-%m-%d")
        time_str = now.strftime("%H:%M:%S")
        
        if signal_data:
            # Signal found - format with colors
            direction = signal_data['direction']
            confidence = f"{signal_data['confidence']}%"
            strategy = signal_data['strategy']
            
            # Color direction based on UP/DOWN
            if direction == "UP":
                direction_colored = f"{Fore.GREEN}{direction:<10}{Style.RESET_ALL}"
            else:
                direction_colored = f"{Fore.RED}{direction:<10}{Style.RESET_ALL}"
            
            # Color confidence based on level
            conf_value = signal_data['confidence']
            if conf_value >= 90:
                confidence_colored = f"{Fore.GREEN}{confidence:<12}{Style.RESET_ALL}"
            elif conf_value >= 80:
                confidence_colored = f"{Fore.YELLOW}{confidence:<12}{Style.RESET_ALL}"
            else:
                confidence_colored = f"{Fore.CYAN}{confidence:<12}{Style.RESET_ALL}"
            
            # Format strategy name
            strategy_short = strategy.replace('Strategy', 'S').replace('_', '-')
            
            output = (
                f"{Fore.BLUE}{date_str:<12}{Style.RESET_ALL} "
                f"{Fore.MAGENTA}{time_str:<8}{Style.RESET_ALL} "
                f"{direction_colored} "
                f"{confidence_colored} "
                f"{Fore.YELLOW}{strategy_short:<20}{Style.RESET_ALL} "
                f"{Fore.RED}{market_price:<13.5f}{Style.RESET_ALL} "
                f"{Fore.GREEN}{'🎯 SIGNAL GENERATED':<25}{Style.RESET_ALL}"
            )
        else:
            # No signal found
            output = (
                f"{Fore.BLUE}{date_str:<12}{Style.RESET_ALL} "
                f"{Fore.MAGENTA}{time_str:<8}{Style.RESET_ALL} "
                f"{Fore.LIGHTBLACK_EX}{'---':<10}{Style.RESET_ALL} "
                f"{Fore.LIGHTBLACK_EX}{'---':<12}{Style.RESET_ALL} "
                f"{Fore.LIGHTBLACK_EX}{'---':<20}{Style.RESET_ALL} "
                f"{Fore.RED}{market_price:<13.5f}{Style.RESET_ALL} "
                f"{Fore.LIGHTBLACK_EX}{'❌ NO SIGNAL FOUND':<25}{Style.RESET_ALL}"
            )
        
        print(output)
    
    def get_current_market_price(self):
        """Get current EUR/USD market price"""
        try:
            price_data = self.oanda_client.get_current_price()
            if price_data:
                # Return mid price
                return (price_data['bid'] + price_data['ask']) / 2
            else:
                return 0.0
        except Exception as e:
            print(f"{Fore.RED}❌ Error fetching price: {e}{Style.RESET_ALL}")
            return 0.0
    
    def wait_for_signal_time(self):
        """Wait until 58 seconds of the minute to fetch data"""
        now = datetime.now()
        current_second = now.second
        
        if current_second < 58:
            # Wait until 58 seconds
            wait_time = 58 - current_second
            print(f"{Fore.CYAN}⏳ Waiting {wait_time} seconds for next signal check...{Style.RESET_ALL}", end='\r')
            time.sleep(wait_time)
        elif current_second >= 58:
            # Wait until next minute + 58 seconds
            wait_time = (60 - current_second) + 58
            print(f"{Fore.CYAN}⏳ Waiting {wait_time} seconds for next signal check...{Style.RESET_ALL}", end='\r')
            time.sleep(wait_time)
    
    def run_signal_generation(self):
        """Main signal generation loop"""
        print(f"{Fore.GREEN}🚀 Starting live signal generation...{Style.RESET_ALL}")
        print(f"{Fore.CYAN}📊 Checking for signals every minute at XX:XX:58{Style.RESET_ALL}")
        print(f"{Fore.YELLOW}⚡ Press Ctrl+C to stop{Style.RESET_ALL}")
        print()
        
        try:
            while True:
                # Wait for the right time (58 seconds)
                self.wait_for_signal_time()
                
                # Clear the waiting message
                print(" " * 60, end='\r')
                
                # Get current market price
                market_price = self.get_current_market_price()
                
                # Generate signal
                try:
                    signal = self.signal_engine.generate_signal()
                    self.format_signal_output(signal, market_price)
                except Exception as e:
                    # If signal generation fails, show no signal
                    self.format_signal_output(None, market_price)
                
                # Wait a bit before next check
                time.sleep(2)
                
        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}⏹️ Signal generation stopped by user{Style.RESET_ALL}")
            print(f"{Fore.GREEN}✅ Bot shutdown complete{Style.RESET_ALL}")

def main():
    """Main function"""
    try:
        # Install colorama if not available
        try:
            import colorama
        except ImportError:
            print("Installing colorama for colored output...")
            os.system("pip install colorama")
            import colorama
        
        # Create and run the bot
        bot = FormattedSignalBot()
        bot.run_signal_generation()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("Make sure you have installed the required packages:")
        print("pip install colorama pandas numpy requests oandapyV20")

if __name__ == "__main__":
    main()
