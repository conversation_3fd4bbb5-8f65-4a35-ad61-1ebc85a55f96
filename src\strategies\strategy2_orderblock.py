"""
Strategy 2: Order Block Rejection with Low Volume (Reversal)
"""
import pandas as pd
import numpy as np
from typing import Dict, Optional
from .base_strategy import BaseStrategy

class OrderBlockStrategy(BaseStrategy):
    """
    Strategy 2: Order Block Rejection with Low Volume (Reversal)
    
    Entry Conditions:
    1. Price touches or enters an order block
    2. Candle gets rejected from the order block and closes away from it
    3. That rejecting candle's volume is lower than the previous candle
    """
    
    def __init__(self):
        super().__init__("Strategy2_OrderBlock")
        
    def analyze(self, data: pd.DataFrame) -> Optional[Dict]:
        """
        Analyze data for order block rejection signals
        
        Args:
            data: DataFrame with OHLCV data
            
        Returns:
            Signal dictionary or None
        """
        if len(data) < 30:  # Need sufficient data
            return None
        
        # Detect order blocks in recent data
        order_blocks = self.detect_order_blocks(data.iloc[-50:] if len(data) > 50 else data)
        
        if not order_blocks:
            return None
        
        # Get the last few candles for analysis
        current_candle = data.iloc[-1]
        previous_candle = data.iloc[-2] if len(data) > 1 else None
        
        if previous_candle is None:
            return None
        
        # Check for rejection from bullish order blocks (bearish reversal)
        bearish_signal = self._check_bearish_reversal(
            data, current_candle, previous_candle, order_blocks
        )
        
        if bearish_signal:
            return bearish_signal
        
        # Check for rejection from bearish order blocks (bullish reversal)
        bullish_signal = self._check_bullish_reversal(
            data, current_candle, previous_candle, order_blocks
        )
        
        return bullish_signal
    
    def _check_bearish_reversal(self, data: pd.DataFrame, current_candle: pd.Series, 
                               previous_candle: pd.Series, order_blocks: list) -> Optional[Dict]:
        """
        Check for bearish reversal from bullish order block
        
        Args:
            data: Full DataFrame
            current_candle: Current candle data
            previous_candle: Previous candle data
            order_blocks: List of detected order blocks
            
        Returns:
            Signal dictionary or None
        """
        # Find relevant bullish order blocks above current price
        relevant_blocks = []
        for block in order_blocks:
            if (block['type'] == 'bullish' and 
                block['low'] <= current_candle['high'] and 
                block['high'] >= current_candle['low']):
                relevant_blocks.append(block)
        
        if not relevant_blocks:
            return None
        
        # Use the most recent relevant order block
        order_block = max(relevant_blocks, key=lambda x: x['start_index'])
        
        conditions_met = []
        
        # 1. Check if price touched or entered the order block
        price_touched_block = False
        if (current_candle['high'] >= order_block['low'] and 
            current_candle['low'] <= order_block['high']):
            price_touched_block = True
            conditions_met.append("Price touched/entered order block")
        
        if not price_touched_block:
            return None
        
        # 2. Check if candle got rejected and closed away from order block
        rejection_occurred = False
        
        # For bearish reversal, we expect:
        # - Price to reach into the bullish order block (above)
        # - Then close below the order block or significantly lower than the high
        
        if current_candle['close'] < order_block['low']:
            # Closed completely below the order block
            rejection_occurred = True
            conditions_met.append("Closed below order block (strong rejection)")
        elif current_candle['close'] < current_candle['high'] - (current_candle['high'] - current_candle['low']) * 0.6:
            # Closed in lower 40% of the candle range after touching order block
            rejection_occurred = True
            conditions_met.append("Closed in lower portion after touching order block")
        
        if not rejection_occurred:
            return None
        
        # 3. Check if volume is lower than previous candle
        if current_candle['volume'] < previous_candle['volume']:
            conditions_met.append("Lower volume than previous candle")
            volume_ratio = current_candle['volume'] / previous_candle['volume']
        else:
            return None
        
        # Additional confirmation: Check if current candle is bearish
        candle_is_bearish = current_candle['close'] < current_candle['open']
        if candle_is_bearish:
            conditions_met.append("Bearish candle formation")
        
        # Calculate rejection strength
        rejection_distance = order_block['low'] - current_candle['close']
        rejection_pips = self.calculate_pips_movement(current_candle['close'], order_block['low'])
        
        # Calculate confidence
        base_confidence = 65
        confidence_boost = 0
        
        # Volume factor (lower volume = higher confidence for reversal)
        if volume_ratio < 0.7:
            confidence_boost += 15
        elif volume_ratio < 0.8:
            confidence_boost += 10
        elif volume_ratio < 0.9:
            confidence_boost += 5
        
        # Rejection strength factor
        if rejection_pips > 15:
            confidence_boost += 15
        elif rejection_pips > 10:
            confidence_boost += 10
        elif rejection_pips > 5:
            confidence_boost += 5
        
        # Order block strength (higher volume = stronger block)
        if order_block['volume'] > data['volume'].iloc[-20:].mean() * 1.5:
            confidence_boost += 10
        
        # Candle type bonus
        if candle_is_bearish:
            confidence_boost += 5
        
        final_confidence = min(95, base_confidence + confidence_boost)
        
        return self.format_signal(
            direction="DOWN",
            confidence=final_confidence,
            current_price=current_candle['close'],
            strategy_name=self.name,
            details={
                'order_block_type': 'bullish',
                'order_block_high': order_block['high'],
                'order_block_low': order_block['low'],
                'order_block_volume': order_block['volume'],
                'volume_ratio': round(volume_ratio, 2),
                'rejection_pips': round(rejection_pips, 1),
                'conditions_met': conditions_met,
                'candle_type': 'bearish' if candle_is_bearish else 'bullish'
            }
        )
    
    def _check_bullish_reversal(self, data: pd.DataFrame, current_candle: pd.Series, 
                               previous_candle: pd.Series, order_blocks: list) -> Optional[Dict]:
        """
        Check for bullish reversal from bearish order block
        
        Args:
            data: Full DataFrame
            current_candle: Current candle data
            previous_candle: Previous candle data
            order_blocks: List of detected order blocks
            
        Returns:
            Signal dictionary or None
        """
        # Find relevant bearish order blocks below current price
        relevant_blocks = []
        for block in order_blocks:
            if (block['type'] == 'bearish' and 
                block['low'] <= current_candle['high'] and 
                block['high'] >= current_candle['low']):
                relevant_blocks.append(block)
        
        if not relevant_blocks:
            return None
        
        # Use the most recent relevant order block
        order_block = max(relevant_blocks, key=lambda x: x['start_index'])
        
        conditions_met = []
        
        # 1. Check if price touched or entered the order block
        price_touched_block = False
        if (current_candle['low'] <= order_block['high'] and 
            current_candle['high'] >= order_block['low']):
            price_touched_block = True
            conditions_met.append("Price touched/entered order block")
        
        if not price_touched_block:
            return None
        
        # 2. Check if candle got rejected and closed away from order block
        rejection_occurred = False
        
        # For bullish reversal, we expect:
        # - Price to reach into the bearish order block (below)
        # - Then close above the order block or significantly higher than the low
        
        if current_candle['close'] > order_block['high']:
            # Closed completely above the order block
            rejection_occurred = True
            conditions_met.append("Closed above order block (strong rejection)")
        elif current_candle['close'] > current_candle['low'] + (current_candle['high'] - current_candle['low']) * 0.6:
            # Closed in upper 40% of the candle range after touching order block
            rejection_occurred = True
            conditions_met.append("Closed in upper portion after touching order block")
        
        if not rejection_occurred:
            return None
        
        # 3. Check if volume is lower than previous candle
        if current_candle['volume'] < previous_candle['volume']:
            conditions_met.append("Lower volume than previous candle")
            volume_ratio = current_candle['volume'] / previous_candle['volume']
        else:
            return None
        
        # Additional confirmation: Check if current candle is bullish
        candle_is_bullish = current_candle['close'] > current_candle['open']
        if candle_is_bullish:
            conditions_met.append("Bullish candle formation")
        
        # Calculate rejection strength
        rejection_distance = current_candle['close'] - order_block['high']
        rejection_pips = self.calculate_pips_movement(order_block['high'], current_candle['close'])
        
        # Calculate confidence
        base_confidence = 65
        confidence_boost = 0
        
        # Volume factor (lower volume = higher confidence for reversal)
        if volume_ratio < 0.7:
            confidence_boost += 15
        elif volume_ratio < 0.8:
            confidence_boost += 10
        elif volume_ratio < 0.9:
            confidence_boost += 5
        
        # Rejection strength factor
        if rejection_pips > 15:
            confidence_boost += 15
        elif rejection_pips > 10:
            confidence_boost += 10
        elif rejection_pips > 5:
            confidence_boost += 5
        
        # Order block strength
        if order_block['volume'] > data['volume'].iloc[-20:].mean() * 1.5:
            confidence_boost += 10
        
        # Candle type bonus
        if candle_is_bullish:
            confidence_boost += 5
        
        final_confidence = min(95, base_confidence + confidence_boost)
        
        return self.format_signal(
            direction="UP",
            confidence=final_confidence,
            current_price=current_candle['close'],
            strategy_name=self.name,
            details={
                'order_block_type': 'bearish',
                'order_block_high': order_block['high'],
                'order_block_low': order_block['low'],
                'order_block_volume': order_block['volume'],
                'volume_ratio': round(volume_ratio, 2),
                'rejection_pips': round(rejection_pips, 1),
                'conditions_met': conditions_met,
                'candle_type': 'bullish' if candle_is_bullish else 'bearish'
            }
        )
