"""
Signal Generation Engine for Binary Trading Bot
"""
import logging
import time
import threading
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, Optional, List
import pandas as pd

from .oanda_client import OandaClient
from .strategies.strategy1_breakout import BreakoutStrategy
from .strategies.strategy2_orderblock import OrderBlockStrategy
from .strategies.strategy3_pattern import PatternReversalStrategy
from config import Config

class SignalEngine:
    """
    Main signal generation engine that coordinates all strategies
    """
    
    def __init__(self):
        self.oanda_client = OandaClient()
        self.strategies = {
            'strategy1': BreakoutStrategy(),
            'strategy2': OrderBlockStrategy(),
            'strategy3': PatternReversalStrategy()
        }
        self.logger = logging.getLogger(__name__)
        self.is_running = False
        self.signal_thread = None
        self.latest_signal = None
        self.signal_history = []
        
    def start_signal_generation(self):
        """Start the signal generation process"""
        if self.is_running:
            self.logger.warning("Signal generation is already running")
            return
        
        # Test OANDA connection first
        if not self.oanda_client.test_connection():
            self.logger.error("Cannot start signal generation - OANDA connection failed")
            return False
        
        self.is_running = True
        self.signal_thread = threading.Thread(target=self._signal_loop, daemon=True)
        self.signal_thread.start()
        self.logger.info("Signal generation started")
        return True
    
    def stop_signal_generation(self):
        """Stop the signal generation process"""
        self.is_running = False
        if self.signal_thread:
            self.signal_thread.join(timeout=5)
        self.logger.info("Signal generation stopped")
    
    def _signal_loop(self):
        """Main signal generation loop"""
        while self.is_running:
            try:
                # Calculate time until next signal check (2 seconds before next minute)
                now = datetime.now()
                seconds_to_wait = 60 - now.second - 2  # 2 seconds before next minute
                
                if seconds_to_wait <= 0:
                    seconds_to_wait += 60
                
                # Wait until 2 seconds before next candle
                time.sleep(seconds_to_wait)
                
                if not self.is_running:
                    break
                
                # Generate signal
                signal = self.generate_signal()
                
                if signal:
                    self.latest_signal = signal
                    self.signal_history.append(signal)
                    
                    # Keep only last 100 signals in memory
                    if len(self.signal_history) > 100:
                        self.signal_history = self.signal_history[-100:]
                    
                    self.logger.info(f"Signal generated: {signal['direction']} "
                                   f"({signal['confidence']}%) by {signal['strategy']}")
                
            except Exception as e:
                self.logger.error(f"Error in signal loop: {e}")
                time.sleep(60)  # Wait a minute before retrying
    
    def generate_signal(self) -> Optional[Dict]:
        """
        Generate trading signal by analyzing all strategies
        
        Returns:
            Signal dictionary or None
        """
        try:
            # Fetch latest market data
            data = self.oanda_client.get_candles(count=Config.LOOKBACK_PERIODS)
            
            if data is None or len(data) < 50:
                self.logger.warning("Insufficient market data for signal generation")
                return None
            
            # Get current price
            current_price_info = self.oanda_client.get_current_price()
            if not current_price_info:
                self.logger.warning("Could not fetch current price")
                return None
            
            current_price = (current_price_info['bid'] + current_price_info['ask']) / 2
            
            # Test each strategy
            signals = []
            
            for strategy_name, strategy in self.strategies.items():
                try:
                    signal = strategy.analyze(data)
                    if signal:
                        # Update current price in signal
                        signal['current_price'] = current_price
                        signal['bid'] = current_price_info['bid']
                        signal['ask'] = current_price_info['ask']
                        signal['spread'] = current_price_info['spread']
                        signals.append(signal)
                        
                        self.logger.info(f"{strategy_name} generated signal: "
                                       f"{signal['direction']} ({signal['confidence']}%)")
                
                except Exception as e:
                    self.logger.error(f"Error in {strategy_name}: {e}")
            
            # If multiple signals, choose the one with highest confidence
            if signals:
                best_signal = max(signals, key=lambda x: x['confidence'])
                
                # Add metadata
                best_signal['all_signals'] = signals
                best_signal['signal_count'] = len(signals)
                best_signal['instrument'] = Config.INSTRUMENT
                
                return best_signal
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error generating signal: {e}")
            return None
    
    def get_latest_signal(self) -> Optional[Dict]:
        """Get the latest generated signal"""
        return self.latest_signal
    
    def get_signal_history(self, limit: int = 50) -> List[Dict]:
        """
        Get signal history
        
        Args:
            limit: Maximum number of signals to return
            
        Returns:
            List of signal dictionaries
        """
        return self.signal_history[-limit:] if self.signal_history else []
    
    def get_strategy_performance(self) -> Dict:
        """
        Get performance statistics for each strategy
        
        Returns:
            Dictionary with strategy performance data
        """
        if not self.signal_history:
            return {}
        
        performance = {}
        
        for strategy_name in self.strategies.keys():
            strategy_signals = [s for s in self.signal_history if s['strategy'] == strategy_name]
            
            if strategy_signals:
                total_signals = len(strategy_signals)
                avg_confidence = sum(s['confidence'] for s in strategy_signals) / total_signals
                up_signals = len([s for s in strategy_signals if s['direction'] == 'UP'])
                down_signals = len([s for s in strategy_signals if s['direction'] == 'DOWN'])
                
                performance[strategy_name] = {
                    'total_signals': total_signals,
                    'average_confidence': round(avg_confidence, 2),
                    'up_signals': up_signals,
                    'down_signals': down_signals,
                    'up_percentage': round((up_signals / total_signals) * 100, 1),
                    'down_percentage': round((down_signals / total_signals) * 100, 1)
                }
            else:
                performance[strategy_name] = {
                    'total_signals': 0,
                    'average_confidence': 0,
                    'up_signals': 0,
                    'down_signals': 0,
                    'up_percentage': 0,
                    'down_percentage': 0
                }
        
        return performance
    
    def is_market_open(self) -> bool:
        """
        Check if forex market is open
        
        Returns:
            True if market is open, False otherwise
        """
        now = datetime.now()
        
        # Forex market is closed from Friday 22:00 GMT to Sunday 22:00 GMT
        # This is a simplified check - in production you'd want more precise market hours
        
        if now.weekday() == 5:  # Saturday
            return False
        elif now.weekday() == 6:  # Sunday
            return now.hour >= 22  # Open from 22:00 GMT on Sunday
        elif now.weekday() == 4:  # Friday
            return now.hour < 22  # Close at 22:00 GMT on Friday
        else:
            return True  # Monday to Thursday - market is open
    
    def get_status(self) -> Dict:
        """
        Get current status of the signal engine
        
        Returns:
            Status dictionary
        """
        return {
            'is_running': self.is_running,
            'market_open': self.is_market_open(),
            'latest_signal': self.latest_signal,
            'total_signals_generated': len(self.signal_history),
            'oanda_connection': self.oanda_client.test_connection(),
            'strategies_loaded': list(self.strategies.keys())
        }
