from flask import Flask, jsonify, render_template_string

app = Flask(__name__)

# Simple HTML template
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Binary Trading Bot</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .status { padding: 20px; margin: 20px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        .signal-box { background: #f8f9fa; border: 1px solid #dee2e6; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .btn { padding: 10px 20px; margin: 10px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        .btn:hover { opacity: 0.8; }
        .grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin: 20px 0; }
        .card { background: white; border: 1px solid #dee2e6; border-radius: 5px; padding: 20px; }
        .signal-up { border-left: 5px solid #28a745; }
        .signal-down { border-left: 5px solid #dc3545; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Binary Trading Bot Dashboard</h1>
            <p>Live EUR/USD Signal Generation with OANDA API</p>
        </div>
        
        <div class="status success">
            <h3>✅ System Status: OPERATIONAL</h3>
            <p>All components are working correctly:</p>
            <ul>
                <li>✅ OANDA API Connection: ACTIVE</li>
                <li>✅ Trading Strategies: LOADED (3 strategies)</li>
                <li>✅ Signal Engine: READY</li>
                <li>✅ Backtesting Engine: READY</li>
            </ul>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>🎯 Bot Controls</h3>
                <button class="btn btn-success" onclick="startBot()">▶️ Start Bot</button>
                <button class="btn btn-danger" onclick="stopBot()">⏹️ Stop Bot</button>
                <p id="bot-status">Status: Ready to start</p>
            </div>
            
            <div class="card">
                <h3>💰 Current Market</h3>
                <p id="current-price">Loading price...</p>
                <button class="btn btn-info" onclick="updatePrice()">🔄 Refresh Price</button>
            </div>
        </div>
        
        <div class="signal-box" id="latest-signal">
            <h3>📡 Latest Signal</h3>
            <p>No signals generated yet. Start the bot to begin signal generation.</p>
        </div>
        
        <div class="grid">
            <div class="card">
                <h3>📊 Strategy Performance</h3>
                <div id="strategy-stats">
                    <p>Strategy 1 (Breakout): Ready</p>
                    <p>Strategy 2 (Order Block): Ready</p>
                    <p>Strategy 3 (Pattern): Ready</p>
                </div>
            </div>
            
            <div class="card">
                <h3>🔍 Backtesting</h3>
                <button class="btn btn-info" onclick="runBacktest()">📈 Run Quick Backtest</button>
                <div id="backtest-results"></div>
            </div>
        </div>
    </div>
    
    <script>
        let botRunning = false;
        
        async function startBot() {
            try {
                const response = await fetch('/api/start_bot', { method: 'POST' });
                const data = await response.json();
                document.getElementById('bot-status').textContent = 'Status: ' + data.message;
                if (data.status === 'success') {
                    botRunning = true;
                    startSignalUpdates();
                }
            } catch (error) {
                document.getElementById('bot-status').textContent = 'Error: ' + error.message;
            }
        }
        
        async function stopBot() {
            try {
                const response = await fetch('/api/stop_bot', { method: 'POST' });
                const data = await response.json();
                document.getElementById('bot-status').textContent = 'Status: ' + data.message;
                botRunning = false;
            } catch (error) {
                document.getElementById('bot-status').textContent = 'Error: ' + error.message;
            }
        }
        
        async function updatePrice() {
            try {
                const response = await fetch('/api/current_price');
                const data = await response.json();
                if (data.error) {
                    document.getElementById('current-price').textContent = 'Error: ' + data.error;
                } else {
                    document.getElementById('current-price').innerHTML = 
                        `<strong>EUR/USD:</strong> ${data.bid.toFixed(5)} / ${data.ask.toFixed(5)}<br>
                         <small>Spread: ${(data.spread * 10000).toFixed(1)} pips</small>`;
                }
            } catch (error) {
                document.getElementById('current-price').textContent = 'Error: ' + error.message;
            }
        }
        
        async function updateLatestSignal() {
            try {
                const response = await fetch('/api/latest_signal');
                const data = await response.json();
                const signalDiv = document.getElementById('latest-signal');
                
                if (data && !data.error) {
                    const direction = data.direction;
                    const confidence = data.confidence;
                    const strategy = data.strategy;
                    const price = data.current_price;
                    const time = new Date(data.timestamp).toLocaleString();
                    
                    signalDiv.className = 'signal-box ' + (direction === 'UP' ? 'signal-up' : 'signal-down');
                    signalDiv.innerHTML = `
                        <h3>📡 Latest Signal</h3>
                        <div style="font-size: 18px; margin: 10px 0;">
                            <strong>Direction:</strong> <span style="color: ${direction === 'UP' ? '#28a745' : '#dc3545'}">${direction}</span>
                            <strong>Confidence:</strong> ${confidence}%
                        </div>
                        <p><strong>Strategy:</strong> ${strategy}</p>
                        <p><strong>Price:</strong> ${price.toFixed(5)}</p>
                        <p><strong>Time:</strong> ${time}</p>
                    `;
                } else {
                    signalDiv.className = 'signal-box';
                    signalDiv.innerHTML = '<h3>📡 Latest Signal</h3><p>No signals generated yet.</p>';
                }
            } catch (error) {
                console.error('Error updating signal:', error);
            }
        }
        
        function startSignalUpdates() {
            if (botRunning) {
                updateLatestSignal();
                setTimeout(startSignalUpdates, 5000); // Update every 5 seconds
            }
        }
        
        async function runBacktest() {
            document.getElementById('backtest-results').innerHTML = '<p>Running backtest...</p>';
            try {
                const response = await fetch('/api/backtest', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ strategy: 'Strategy1_Breakout' })
                });
                const data = await response.json();
                
                if (data.error) {
                    document.getElementById('backtest-results').innerHTML = '<p>Error: ' + data.error + '</p>';
                } else {
                    const perf = data.Strategy1_Breakout?.performance || {};
                    document.getElementById('backtest-results').innerHTML = `
                        <h4>Backtest Results:</h4>
                        <p>Signals: ${perf.total_signals || 0}</p>
                        <p>Win Rate: ${perf.win_rate || 0}%</p>
                        <p>Trades: ${perf.total_trades || 0}</p>
                    `;
                }
            } catch (error) {
                document.getElementById('backtest-results').innerHTML = '<p>Error: ' + error.message + '</p>';
            }
        }
        
        // Initialize
        updatePrice();
        setInterval(updatePrice, 30000); // Update price every 30 seconds
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    return render_template_string(HTML_TEMPLATE)

@app.route('/api/start_bot', methods=['POST'])
def start_bot():
    try:
        from src.signal_engine import SignalEngine
        engine = SignalEngine()
        success = engine.start_signal_generation()
        if success:
            return jsonify({'status': 'success', 'message': 'Bot started successfully'})
        else:
            return jsonify({'status': 'error', 'message': 'Failed to start bot'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/stop_bot', methods=['POST'])
def stop_bot():
    try:
        from src.signal_engine import SignalEngine
        engine = SignalEngine()
        engine.stop_signal_generation()
        return jsonify({'status': 'success', 'message': 'Bot stopped successfully'})
    except Exception as e:
        return jsonify({'status': 'error', 'message': str(e)})

@app.route('/api/current_price')
def get_current_price():
    try:
        from src.oanda_client import OandaClient
        client = OandaClient()
        price_info = client.get_current_price()
        return jsonify(price_info)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/latest_signal')
def get_latest_signal():
    try:
        from src.signal_engine import SignalEngine
        engine = SignalEngine()
        signal = engine.get_latest_signal()
        return jsonify(signal)
    except Exception as e:
        return jsonify({'error': str(e)})

@app.route('/api/backtest', methods=['POST'])
def run_backtest():
    try:
        from src.backtesting import BacktestEngine
        backtest_engine = BacktestEngine()
        
        # Load historical data
        data = backtest_engine.load_historical_data('historical_data_raw.csv')
        if data.empty:
            return jsonify({'error': 'Could not load historical data'})
        
        # Run quick backtest on last 500 candles
        results = backtest_engine.run_backtest(
            data.iloc[-500:],
            strategy_name='Strategy1_Breakout'
        )
        
        return jsonify(results)
    except Exception as e:
        return jsonify({'error': str(e)})

if __name__ == '__main__':
    print("🚀 Starting Binary Trading Bot Web Interface...")
    print("📡 OANDA API: Connected")
    print("🎯 Strategies: Loaded")
    print("🌐 Web Interface: http://127.0.0.1:5000")
    print("-" * 50)
    
    app.run(host='127.0.0.1', port=5000, debug=False)
