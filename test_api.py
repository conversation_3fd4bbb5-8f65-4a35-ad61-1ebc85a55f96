#!/usr/bin/env python3
"""
Test the API endpoints
"""
import requests
import json
import time

def test_backtest_api():
    print("🧪 Testing Backtesting API...")
    
    start_time = time.time()
    
    try:
        response = requests.post(
            'http://127.0.0.1:8080/api/run_backtest',
            json={
                'strategy': 'Strategy1_Breakout',
                'candles_count': 100
            },
            timeout=10
        )
        
        end_time = time.time()
        
        if response.status_code == 200:
            data = response.json()
            
            print(f"⚡ API Response time: {end_time - start_time:.2f} seconds")
            
            if 'error' in data:
                print(f"❌ Error: {data['error']}")
                return
            
            # Extract results
            perf = data['Strategy1_Breakout']['performance']
            breakdown = perf['trade_breakdown']
            dir_analysis = perf['direction_analysis']
            
            print("✅ Backtesting API Working!")
            print(f"📊 Results:")
            print(f"   Total Trades: {perf['total_trades']}")
            print(f"   ✅ Wins: {breakdown['wins']}")
            print(f"   ❌ Losses: {breakdown['losses']}")
            print(f"   Win Rate: {perf['win_rate']}%")
            print(f"   Profit/Loss: {perf['total_profit_loss']}")
            
            print(f"\n🎯 Direction Breakdown:")
            print(f"   UP: {dir_analysis['up_trades']} trades ({dir_analysis['up_wins']} wins, {dir_analysis['up_losses']} losses)")
            print(f"   DOWN: {dir_analysis['down_trades']} trades ({dir_analysis['down_wins']} wins, {dir_analysis['down_losses']} losses)")
            
        else:
            print(f"❌ API Error: Status {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    test_backtest_api()
