// Dashboard JavaScript for Binary Trading Bot

class TradingBotDashboard {
    constructor() {
        this.isRunning = false;
        this.updateInterval = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.updateStatus();
        this.updateCurrentPrice();
        this.updateStrategyPerformance();
        this.updateSignalHistory();
        
        // Start periodic updates
        this.startPeriodicUpdates();
    }

    bindEvents() {
        document.getElementById('start-bot').addEventListener('click', () => this.startBot());
        document.getElementById('stop-bot').addEventListener('click', () => this.stopBot());
        document.getElementById('run-backtest').addEventListener('click', () => this.runBacktest());
    }

    startPeriodicUpdates() {
        // Update every 5 seconds
        this.updateInterval = setInterval(() => {
            this.updateStatus();
            this.updateLatestSignal();
            this.updateCurrentPrice();
            this.updateStrategyPerformance();
            this.updateSignalHistory();
        }, 5000);
    }

    stopPeriodicUpdates() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    }

    async startBot() {
        try {
            const response = await fetch('/api/start_bot', { method: 'POST' });
            const data = await response.json();
            
            if (data.status === 'success') {
                this.showAlert('Bot started successfully!', 'success');
                this.updateStatus();
            } else {
                this.showAlert(data.message, 'danger');
            }
        } catch (error) {
            this.showAlert('Error starting bot: ' + error.message, 'danger');
        }
    }

    async stopBot() {
        try {
            const response = await fetch('/api/stop_bot', { method: 'POST' });
            const data = await response.json();
            
            if (data.status === 'success') {
                this.showAlert('Bot stopped successfully!', 'warning');
                this.updateStatus();
            } else {
                this.showAlert(data.message, 'danger');
            }
        } catch (error) {
            this.showAlert('Error stopping bot: ' + error.message, 'danger');
        }
    }

    async updateStatus() {
        try {
            const response = await fetch('/api/status');
            const status = await response.json();
            
            this.isRunning = status.is_running;
            
            // Update status indicator
            const statusIndicator = document.querySelector('.status-indicator');
            const statusText = document.getElementById('status-text');
            
            if (status.is_running) {
                statusIndicator.className = 'status-indicator status-running';
                statusText.textContent = 'Running';
            } else {
                statusIndicator.className = 'status-indicator status-stopped';
                statusText.textContent = 'Stopped';
            }
            
            // Update market status
            const marketStatus = document.getElementById('market-status');
            if (status.market_open) {
                marketStatus.textContent = 'Open';
                marketStatus.className = 'badge bg-success';
            } else {
                marketStatus.textContent = 'Closed';
                marketStatus.className = 'badge bg-danger';
            }
            
        } catch (error) {
            console.error('Error updating status:', error);
        }
    }

    async updateLatestSignal() {
        try {
            const response = await fetch('/api/latest_signal');
            const signal = await response.json();
            
            const signalCard = document.getElementById('latest-signal-card');
            const signalContent = document.getElementById('latest-signal-content');
            
            if (signal && !signal.error) {
                // Update card styling based on direction
                signalCard.className = `card signal-card ${signal.direction.toLowerCase()}`;
                
                const confidencePercent = signal.confidence;
                const confidenceColor = confidencePercent >= 80 ? 'success' : 
                                      confidencePercent >= 60 ? 'warning' : 'danger';
                
                signalContent.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h4><span class="badge bg-${signal.direction === 'UP' ? 'success' : 'danger'}">${signal.direction}</span></h4>
                            <p><strong>Strategy:</strong> ${signal.strategy}</p>
                            <p><strong>Price:</strong> ${signal.current_price.toFixed(5)}</p>
                            <p><strong>Time:</strong> ${new Date(signal.timestamp).toLocaleString()}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Confidence:</strong></p>
                            <div class="confidence-bar">
                                <div class="confidence-indicator" style="left: ${confidencePercent}%"></div>
                            </div>
                            <p class="mt-2"><span class="badge bg-${confidenceColor}">${confidencePercent}%</span></p>
                        </div>
                    </div>
                `;
            } else {
                signalCard.className = 'card signal-card';
                signalContent.innerHTML = '<p class="text-muted">No signals generated yet.</p>';
            }
        } catch (error) {
            console.error('Error updating latest signal:', error);
        }
    }

    async updateCurrentPrice() {
        try {
            const response = await fetch('/api/current_price');
            const priceData = await response.json();
            
            if (priceData && !priceData.error) {
                const midPrice = (priceData.bid + priceData.ask) / 2;
                const spreadPips = (priceData.spread / 0.0001).toFixed(1);
                
                document.getElementById('current-price').textContent = midPrice.toFixed(5);
                document.getElementById('bid-price').textContent = priceData.bid.toFixed(5);
                document.getElementById('ask-price').textContent = priceData.ask.toFixed(5);
                document.getElementById('spread').textContent = spreadPips;
            }
        } catch (error) {
            console.error('Error updating current price:', error);
        }
    }

    async updateStrategyPerformance() {
        try {
            const response = await fetch('/api/strategy_performance');
            const performance = await response.json();
            
            const strategyCards = document.getElementById('strategy-cards');
            
            if (Object.keys(performance).length === 0) {
                strategyCards.innerHTML = '<p class="text-muted">No performance data available yet.</p>';
                return;
            }
            
            let cardsHtml = '';
            for (const [strategyName, data] of Object.entries(performance)) {
                const winRate = data.total_signals > 0 ? 
                    ((data.up_signals + data.down_signals) / data.total_signals * 100).toFixed(1) : 0;
                
                cardsHtml += `
                    <div class="col-md-4">
                        <div class="strategy-card">
                            <h6>${strategyName.replace('Strategy', 'Strategy ')}</h6>
                            <p><strong>Signals:</strong> ${data.total_signals}</p>
                            <p><strong>Avg Confidence:</strong> ${data.average_confidence}%</p>
                            <p><strong>UP/DOWN:</strong> ${data.up_signals}/${data.down_signals}</p>
                        </div>
                    </div>
                `;
            }
            
            strategyCards.innerHTML = cardsHtml;
        } catch (error) {
            console.error('Error updating strategy performance:', error);
        }
    }

    async updateSignalHistory() {
        try {
            const response = await fetch('/api/signal_history?limit=20');
            const history = await response.json();
            
            const tbody = document.getElementById('signal-history-tbody');
            
            if (!history || history.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-muted text-center">No signal history available</td></tr>';
                return;
            }
            
            let rowsHtml = '';
            history.reverse().forEach(signal => {
                const time = new Date(signal.timestamp).toLocaleString();
                const directionBadge = signal.direction === 'UP' ? 'success' : 'danger';
                const confidenceBadge = signal.confidence >= 80 ? 'success' : 
                                      signal.confidence >= 60 ? 'warning' : 'danger';
                
                rowsHtml += `
                    <tr>
                        <td>${time}</td>
                        <td>${signal.strategy.replace('Strategy', 'Strategy ')}</td>
                        <td><span class="badge bg-${directionBadge}">${signal.direction}</span></td>
                        <td><span class="badge bg-${confidenceBadge}">${signal.confidence}%</span></td>
                        <td>${signal.current_price.toFixed(5)}</td>
                        <td><button class="btn btn-sm btn-outline-info" onclick="showSignalDetails('${JSON.stringify(signal).replace(/'/g, "\\'")}')">Details</button></td>
                    </tr>
                `;
            });
            
            tbody.innerHTML = rowsHtml;
        } catch (error) {
            console.error('Error updating signal history:', error);
        }
    }

    async runBacktest() {
        const strategy = document.getElementById('strategy-select').value;
        const startDate = document.getElementById('start-date').value;
        const endDate = document.getElementById('end-date').value;
        
        const runButton = document.getElementById('run-backtest');
        runButton.disabled = true;
        runButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Running...';
        
        try {
            const response = await fetch('/api/backtest', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    strategy: strategy,
                    start_date: startDate,
                    end_date: endDate
                })
            });
            
            const results = await response.json();
            
            if (results.error) {
                this.showAlert('Backtest error: ' + results.error, 'danger');
            } else {
                this.displayBacktestResults(results);
            }
        } catch (error) {
            this.showAlert('Error running backtest: ' + error.message, 'danger');
        } finally {
            runButton.disabled = false;
            runButton.innerHTML = '<i class="fas fa-play"></i> Run Backtest';
        }
    }

    displayBacktestResults(results) {
        const resultsDiv = document.getElementById('backtest-results');
        
        let html = '<h5>Backtest Results</h5>';
        
        if (results.summary) {
            html += `
                <div class="alert alert-info">
                    <strong>Summary:</strong> Best Strategy: ${results.summary.best_strategy || 'N/A'} 
                    (${results.summary.best_win_rate || 0}% win rate)
                </div>
            `;
        }
        
        // Display results for each strategy
        for (const [strategyName, strategyResults] of Object.entries(results)) {
            if (strategyName === 'summary' || strategyName === 'data_info') continue;
            
            const perf = strategyResults.performance;
            html += `
                <div class="card mb-3">
                    <div class="card-header">
                        <h6>${strategyName.replace('Strategy', 'Strategy ')}</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Total Signals:</strong> ${perf.total_signals}<br>
                                <strong>Total Trades:</strong> ${perf.total_trades}
                            </div>
                            <div class="col-md-3">
                                <strong>Win Rate:</strong> ${perf.win_rate}%<br>
                                <strong>Profit/Loss:</strong> ${perf.total_profit_loss}
                            </div>
                            <div class="col-md-3">
                                <strong>UP Trades:</strong> ${perf.direction_analysis.up_trades}<br>
                                <strong>DOWN Trades:</strong> ${perf.direction_analysis.down_trades}
                            </div>
                            <div class="col-md-3">
                                <strong>Avg Confidence:</strong> ${perf.average_confidence}%
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
        
        resultsDiv.innerHTML = html;
        resultsDiv.style.display = 'block';
    }

    showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
}

// Global function to show signal details
function showSignalDetails(signalJson) {
    try {
        const signal = JSON.parse(signalJson);
        
        let detailsHtml = `
            <h6>Signal Details</h6>
            <p><strong>Strategy:</strong> ${signal.strategy}</p>
            <p><strong>Direction:</strong> ${signal.direction}</p>
            <p><strong>Confidence:</strong> ${signal.confidence}%</p>
            <p><strong>Price:</strong> ${signal.current_price}</p>
            <p><strong>Time:</strong> ${new Date(signal.timestamp).toLocaleString()}</p>
        `;
        
        if (signal.details) {
            detailsHtml += '<h6>Additional Details:</h6>';
            for (const [key, value] of Object.entries(signal.details)) {
                detailsHtml += `<p><strong>${key}:</strong> ${JSON.stringify(value)}</p>`;
            }
        }
        
        // Create modal
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Signal Details</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        ${detailsHtml}
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // Remove modal from DOM when hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
        
    } catch (error) {
        console.error('Error showing signal details:', error);
    }
}

// Initialize dashboard when page loads
document.addEventListener('DOMContentLoaded', () => {
    new TradingBotDashboard();
});
