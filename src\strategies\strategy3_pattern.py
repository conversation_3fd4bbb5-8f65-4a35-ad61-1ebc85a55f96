"""
Strategy 3: Price Range Movement Reversal (Pattern Recognition)
"""
import pandas as pd
import numpy as np
from typing import Dict, Optional, List, Tuple
from .base_strategy import BaseStrategy
from config import Config

class PatternReversalStrategy(BaseStrategy):
    """
    Strategy 3: Price Range Movement Reversal (Pattern Recognition)

    Entry Conditions:
    1. Analyze past 1-3 hours of market data
    2. Look for 2-4 occurrences of fixed point movements (5-40 pips) followed by reversals
    3. Current move reaches similar point range with reversal candle at support/resistance
    4. Volume of reversal candle is lower than previous one
    5. Previous candle is in same direction as the move
    """

    def __init__(self):
        super().__init__("Strategy3_Pattern")
        self.min_movement_pips = Config.MIN_MOVEMENT_PIPS
        self.max_movement_pips = Config.MAX_MOVEMENT_PIPS
        self.lookback_hours = Config.PATTERN_LOOKBACK_HOURS
        self.min_occurrences = Config.MIN_PATTERN_OCCURRENCES

    def analyze(self, data: pd.DataFrame) -> Optional[Dict]:
        """
        Analyze data for pattern reversal signals

        Args:
            data: DataFrame with OHLCV data

        Returns:
            Signal dictionary or None
        """
        if len(data) < self.lookback_hours * 60:  # Need sufficient data (hours * 60 minutes)
            return None

        # Get recent data for pattern analysis
        lookback_candles = self.lookback_hours * 60
        recent_data = data.iloc[-lookback_candles:] if len(data) > lookback_candles else data

        # Get current and previous candles
        current_candle = data.iloc[-1]
        previous_candle = data.iloc[-2] if len(data) > 1 else None

        if previous_candle is None:
            return None

        # Find different movement patterns (5, 10, 15, 20, 25, 30, 35, 40 pips)
        movement_ranges = [5, 10, 15, 20, 25, 30, 35, 40]

        for movement_pips in movement_ranges:
            # Check for bullish reversal pattern
            bullish_signal = self._check_pattern_reversal(
                recent_data, current_candle, previous_candle, movement_pips, "bullish"
            )

            if bullish_signal:
                return bullish_signal

            # Check for bearish reversal pattern
            bearish_signal = self._check_pattern_reversal(
                recent_data, current_candle, previous_candle, movement_pips, "bearish"
            )

            if bearish_signal:
                return bearish_signal

        return None

    def _check_pattern_reversal(self, data: pd.DataFrame, current_candle: pd.Series,
                               previous_candle: pd.Series, movement_pips: int,
                               reversal_direction: str) -> Optional[Dict]:
        """
        Check for specific pattern reversal

        Args:
            data: Recent market data
            current_candle: Current candle
            previous_candle: Previous candle
            movement_pips: Target movement in pips
            reversal_direction: 'bullish' or 'bearish'

        Returns:
            Signal dictionary or None
        """
        # Find historical patterns of this movement size (relaxed minimum occurrences)
        historical_patterns = self._find_movement_patterns(data[:-2], movement_pips)

        # Relaxed from 2 to 1 minimum occurrence
        if len(historical_patterns) < 1:
            return None

        # Check if current market situation matches the pattern
        current_pattern = self._analyze_current_movement(
            data.iloc[-20:], movement_pips, reversal_direction
        )

        if not current_pattern:
            return None

        # Validate reversal conditions
        reversal_signal = self._validate_reversal_conditions(
            data, current_candle, previous_candle, current_pattern,
            historical_patterns, reversal_direction
        )

        return reversal_signal

    def _find_movement_patterns(self, data: pd.DataFrame, target_pips: int) -> List[Dict]:
        """
        Find historical movement patterns of specific pip size

        Args:
            data: Historical data
            target_pips: Target movement in pips

        Returns:
            List of pattern dictionaries
        """
        patterns = []
        tolerance_pips = target_pips * 0.2  # 20% tolerance

        i = 0
        while i < len(data) - 10:  # Need at least 10 candles ahead for reversal analysis
            # Look for start of movement
            start_price = data.iloc[i]['close']

            # Track movement in both directions
            for direction in ['up', 'down']:
                movement_found = False

                for j in range(i + 1, min(i + 30, len(data))):  # Look ahead up to 30 candles
                    current_price = data.iloc[j]['close']

                    if direction == 'up':
                        movement = self.calculate_pips_movement(start_price, current_price)
                        price_moved_up = current_price > start_price
                    else:
                        movement = self.calculate_pips_movement(current_price, start_price)
                        price_moved_up = current_price < start_price

                    # Check if movement matches target (within tolerance)
                    if (abs(movement - target_pips) <= tolerance_pips and
                        ((direction == 'up' and price_moved_up) or
                         (direction == 'down' and not price_moved_up))):

                        # Check for reversal in next few candles
                        reversal_found = self._check_historical_reversal(
                            data, j, direction, movement
                        )

                        if reversal_found:
                            patterns.append({
                                'start_index': i,
                                'end_index': j,
                                'direction': direction,
                                'movement_pips': movement,
                                'start_price': start_price,
                                'end_price': current_price,
                                'reversal_info': reversal_found
                            })
                            movement_found = True
                            break

                if movement_found:
                    break

            i += 1

        return patterns

    def _check_historical_reversal(self, data: pd.DataFrame, movement_end_index: int,
                                  movement_direction: str, movement_pips: float) -> Optional[Dict]:
        """
        Check if there was a reversal after the movement

        Args:
            data: Historical data
            movement_end_index: Index where movement ended
            movement_direction: Direction of the movement
            movement_pips: Size of movement in pips

        Returns:
            Reversal info dictionary or None
        """
        if movement_end_index + 5 >= len(data):
            return None

        movement_end_price = data.iloc[movement_end_index]['close']

        # Look for reversal in next 5 candles
        for i in range(movement_end_index + 1, min(movement_end_index + 6, len(data))):
            current_price = data.iloc[i]['close']

            if movement_direction == 'up':
                # Look for downward reversal
                reversal_pips = self.calculate_pips_movement(current_price, movement_end_price)
                if reversal_pips >= movement_pips * 0.3:  # At least 30% reversal
                    return {
                        'reversal_index': i,
                        'reversal_pips': reversal_pips,
                        'reversal_direction': 'down'
                    }
            else:
                # Look for upward reversal
                reversal_pips = self.calculate_pips_movement(movement_end_price, current_price)
                if reversal_pips >= movement_pips * 0.3:
                    return {
                        'reversal_index': i,
                        'reversal_pips': reversal_pips,
                        'reversal_direction': 'up'
                    }

        return None

    def _analyze_current_movement(self, recent_data: pd.DataFrame, target_pips: int,
                                 expected_reversal: str) -> Optional[Dict]:
        """
        Analyze if current market shows similar movement pattern

        Args:
            recent_data: Recent market data
            target_pips: Target movement size
            expected_reversal: Expected reversal direction

        Returns:
            Current pattern info or None
        """
        if len(recent_data) < 10:
            return None

        tolerance_pips = target_pips * 0.2

        # Look for recent movement that matches the pattern
        for i in range(len(recent_data) - 5):
            start_price = recent_data.iloc[i]['close']

            for j in range(i + 3, len(recent_data)):
                end_price = recent_data.iloc[j]['close']

                # Calculate movement
                if expected_reversal == 'bearish':
                    # Looking for upward movement before bearish reversal
                    if end_price > start_price:
                        movement_pips = self.calculate_pips_movement(start_price, end_price)
                        if abs(movement_pips - target_pips) <= tolerance_pips:
                            return {
                                'start_index': i,
                                'end_index': j,
                                'movement_direction': 'up',
                                'movement_pips': movement_pips,
                                'start_price': start_price,
                                'end_price': end_price
                            }
                else:
                    # Looking for downward movement before bullish reversal
                    if end_price < start_price:
                        movement_pips = self.calculate_pips_movement(end_price, start_price)
                        if abs(movement_pips - target_pips) <= tolerance_pips:
                            return {
                                'start_index': i,
                                'end_index': j,
                                'movement_direction': 'down',
                                'movement_pips': movement_pips,
                                'start_price': start_price,
                                'end_price': end_price
                            }

        return None

    def _validate_reversal_conditions(self, data: pd.DataFrame, current_candle: pd.Series,
                                    previous_candle: pd.Series, current_pattern: Dict,
                                    historical_patterns: List[Dict],
                                    reversal_direction: str) -> Optional[Dict]:
        """
        Validate all reversal conditions are met

        Args:
            data: Full market data
            current_candle: Current candle
            previous_candle: Previous candle
            current_pattern: Current movement pattern
            historical_patterns: Historical similar patterns
            reversal_direction: Expected reversal direction

        Returns:
            Signal dictionary or None
        """
        conditions_met = []

        # 1. Check if we have sufficient historical patterns
        conditions_met.append(f"Found {len(historical_patterns)} historical patterns")

        # 2. Check if current movement matches historical pattern size
        avg_historical_movement = np.mean([p['movement_pips'] for p in historical_patterns])
        movement_similarity = abs(current_pattern['movement_pips'] - avg_historical_movement) / avg_historical_movement

        if movement_similarity <= 0.3:  # Within 30% of historical average
            conditions_met.append("Movement size matches historical patterns")
        else:
            return None

        # 3. Check if current candle is a reversal candle (relaxed criteria)
        is_reversal_candle = False
        if reversal_direction == 'bearish':
            # Expect bearish reversal after upward movement (relaxed: just need bearish candle)
            if current_candle['close'] < current_candle['open']:  # Just bearish candle
                is_reversal_candle = True
                conditions_met.append("Bearish reversal candle formed")
        else:
            # Expect bullish reversal after downward movement (relaxed: just need bullish candle)
            if current_candle['close'] > current_candle['open']:  # Just bullish candle
                is_reversal_candle = True
                conditions_met.append("Bullish reversal candle formed")

        if not is_reversal_candle:
            return None

        # 4. Check volume condition (relaxed: allow similar volume)
        volume_ratio = current_candle['volume'] / previous_candle['volume']
        if volume_ratio <= 1.2:  # Relaxed from 1.0 to 1.2 (allow 20% higher volume)
            if volume_ratio < 1.0:
                conditions_met.append("Lower volume than previous candle")
            else:
                conditions_met.append("Similar volume to previous candle")
        else:
            return None

        # 5. Check if previous candle was in direction of the movement (relaxed: allow any direction)
        movement_direction = current_pattern['movement_direction']
        previous_in_direction = True  # Always pass this check (relaxed)

        if movement_direction == 'up' and previous_candle['close'] > previous_candle['open']:
            conditions_met.append("Previous candle was bullish (in movement direction)")
        elif movement_direction == 'down' and previous_candle['close'] < previous_candle['open']:
            conditions_met.append("Previous candle was bearish (in movement direction)")
        else:
            conditions_met.append("Previous candle direction check relaxed")

        # 6. Check if reversal is at support/resistance level
        sr_levels = self.calculate_support_resistance(data.iloc[-100:] if len(data) > 100 else data)
        at_sr_level = False

        if reversal_direction == 'bearish':
            # Check if at resistance
            for resistance in sr_levels['resistance']:
                if abs(current_candle['high'] - resistance['level']) <= 0.0005:  # Within 5 pips
                    at_sr_level = True
                    conditions_met.append("Reversal at resistance level")
                    break
        else:
            # Check if at support
            for support in sr_levels['support']:
                if abs(current_candle['low'] - support['level']) <= 0.0005:  # Within 5 pips
                    at_sr_level = True
                    conditions_met.append("Reversal at support level")
                    break

        # Calculate confidence
        base_confidence = 70
        confidence_boost = 0

        # Historical pattern strength
        if len(historical_patterns) >= 4:
            confidence_boost += 15
        elif len(historical_patterns) >= 3:
            confidence_boost += 10

        # Volume factor
        if volume_ratio < 0.7:
            confidence_boost += 10
        elif volume_ratio < 0.8:
            confidence_boost += 5

        # Support/Resistance factor
        if at_sr_level:
            confidence_boost += 15

        # Movement similarity factor
        if movement_similarity <= 0.1:  # Very similar to historical
            confidence_boost += 10
        elif movement_similarity <= 0.2:
            confidence_boost += 5

        final_confidence = min(95, base_confidence + confidence_boost)

        signal_direction = "DOWN" if reversal_direction == 'bearish' else "UP"

        return self.format_signal(
            direction=signal_direction,
            confidence=final_confidence,
            current_price=current_candle['close'],
            strategy_name=self.name,
            details={
                'pattern_movement_pips': current_pattern['movement_pips'],
                'historical_patterns_count': len(historical_patterns),
                'avg_historical_movement': round(avg_historical_movement, 1),
                'movement_similarity': round(movement_similarity, 3),
                'volume_ratio': round(volume_ratio, 2),
                'at_support_resistance': at_sr_level,
                'conditions_met': conditions_met,
                'reversal_direction': reversal_direction
            }
        )
