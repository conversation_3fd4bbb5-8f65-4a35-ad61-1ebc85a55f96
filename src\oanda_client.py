"""
OANDA API Client for fetching live market data
"""
import requests
import json
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import pandas as pd
from config import Config

class OandaClient:
    def __init__(self):
        self.base_url = Config.OANDA_BASE_URL
        self.headers = Config.get_oanda_headers()
        self.account_id = Config.OANDA_ACCOUNT_ID
        self.logger = logging.getLogger(__name__)
        
    def get_candles(self, instrument: str = None, count: int = 180, granularity: str = None) -> Optional[pd.DataFrame]:
        """
        Fetch candlestick data from OANDA
        
        Args:
            instrument: Trading instrument (e.g., 'EUR_USD')
            count: Number of candles to fetch
            granularity: Timeframe (e.g., 'M1' for 1-minute)
            
        Returns:
            DataFrame with OHLCV data
        """
        instrument = instrument or Config.INSTRUMENT
        granularity = granularity or Config.TIMEFRAME
        
        url = f"{self.base_url}/v3/instruments/{instrument}/candles"
        params = {
            'count': count,
            'granularity': granularity,
            'price': 'M'  # Mid prices
        }
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            candles = data.get('candles', [])
            
            if not candles:
                self.logger.warning("No candle data received")
                return None
                
            # Convert to DataFrame
            df_data = []
            for candle in candles:
                if candle['complete']:  # Only use complete candles
                    df_data.append({
                        'time': candle['time'],
                        'open': float(candle['mid']['o']),
                        'high': float(candle['mid']['h']),
                        'low': float(candle['mid']['l']),
                        'close': float(candle['mid']['c']),
                        'volume': int(candle['volume'])
                    })
            
            if not df_data:
                self.logger.warning("No complete candles found")
                return None
                
            df = pd.DataFrame(df_data)
            df['time'] = pd.to_datetime(df['time'])
            df.set_index('time', inplace=True)
            
            # Add direction column
            df['direction'] = df.apply(lambda row: 'bullish' if row['close'] > row['open'] 
                                     else 'bearish' if row['close'] < row['open'] 
                                     else 'neutral', axis=1)
            
            self.logger.info(f"Fetched {len(df)} candles for {instrument}")
            return df
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching candles: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
            return None
    
    def get_current_price(self, instrument: str = None) -> Optional[Dict]:
        """
        Get current bid/ask prices for an instrument
        
        Args:
            instrument: Trading instrument
            
        Returns:
            Dictionary with current prices
        """
        instrument = instrument or Config.INSTRUMENT
        
        url = f"{self.base_url}/v3/accounts/{self.account_id}/pricing"
        params = {'instruments': instrument}
        
        try:
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            data = response.json()
            prices = data.get('prices', [])
            
            if prices:
                price_data = prices[0]
                return {
                    'instrument': price_data['instrument'],
                    'bid': float(price_data['bids'][0]['price']),
                    'ask': float(price_data['asks'][0]['price']),
                    'spread': float(price_data['asks'][0]['price']) - float(price_data['bids'][0]['price']),
                    'time': price_data['time']
                }
            
            return None
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Error fetching current price: {e}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error: {e}")
            return None
    
    def test_connection(self) -> bool:
        """
        Test connection to OANDA API
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            url = f"{self.base_url}/v3/accounts/{self.account_id}"
            response = requests.get(url, headers=self.headers)
            response.raise_for_status()
            
            self.logger.info("OANDA API connection successful")
            return True
            
        except requests.exceptions.RequestException as e:
            self.logger.error(f"OANDA API connection failed: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error testing connection: {e}")
            return False
