#!/usr/bin/env python3
"""
Simple Binary Trading Bot with Clear Formatted Output
"""
import sys
import os
import time
from datetime import datetime

# Add src directory to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

class SimpleFormattedBot:
    def __init__(self):
        self.setup_signal_engine()
        self.print_header()
    
    def setup_signal_engine(self):
        """Initialize the signal engine"""
        try:
            from src.signal_engine import SignalEngine
            from src.oanda_client import OandaClient
            
            self.signal_engine = SignalEngine()
            self.oanda_client = OandaClient()
            print("✅ OANDA API Connected Successfully")
            print("✅ All 3 Strategies Loaded")
            print("📡 Monitoring EUR/USD on 1-minute candles")
            print()
        except Exception as e:
            print(f"❌ Error initializing bot: {e}")
            sys.exit(1)
    
    def print_header(self):
        """Print the formatted table header"""
        print("=" * 130)
        print("🚀 BINARY TRADING BOT - LIVE SIGNAL GENERATION")
        print("=" * 130)
        print()
        
        # Table header with proper spacing
        header = f"{'DATE':<12} {'TIME':<8} {'DIRECTION':<10} {'CONFIDENCE':<12} {'STRATEGY':<20} {'MARKET PRICE':<13} {'STATUS':<25}"
        print(header)
        print("-" * 130)
    
    def format_signal_output(self, signal_data, market_price):
        """Format signal output in table format"""
        now = datetime.now()
        date_str = now.strftime("%Y-%m-%d")
        time_str = now.strftime("%H:%M:%S")
        
        if signal_data:
            # Signal found
            direction = signal_data['direction']
            confidence = f"{signal_data['confidence']}%"
            strategy = signal_data['strategy'].replace('Strategy', 'S').replace('_', '-')
            status = "🎯 SIGNAL GENERATED"
            
            output = f"{date_str:<12} {time_str:<8} {direction:<10} {confidence:<12} {strategy:<20} {market_price:<13.5f} {status:<25}"
        else:
            # No signal found
            output = f"{date_str:<12} {time_str:<8} {'---':<10} {'---':<12} {'---':<20} {market_price:<13.5f} {'❌ NO SIGNAL FOUND':<25}"
        
        print(output)
    
    def get_current_market_price(self):
        """Get current EUR/USD market price"""
        try:
            price_data = self.oanda_client.get_current_price()
            if price_data:
                return (price_data['bid'] + price_data['ask']) / 2
            else:
                return 0.0
        except Exception as e:
            print(f"❌ Error fetching price: {e}")
            return 0.0
    
    def wait_for_signal_time(self):
        """Wait until 58 seconds of the minute to fetch data"""
        now = datetime.now()
        current_second = now.second
        
        if current_second < 58:
            wait_time = 58 - current_second
            print(f"⏳ Waiting {wait_time} seconds for next signal check...", end='\r')
            time.sleep(wait_time)
        elif current_second >= 58:
            wait_time = (60 - current_second) + 58
            print(f"⏳ Waiting {wait_time} seconds for next signal check...", end='\r')
            time.sleep(wait_time)
    
    def run_signal_generation(self):
        """Main signal generation loop"""
        print("🚀 Starting live signal generation...")
        print("📊 Fetching market data at XX:XX:58 every minute")
        print("⚡ Press Ctrl+C to stop")
        print()
        
        try:
            while True:
                # Wait for the right time (58 seconds)
                self.wait_for_signal_time()
                
                # Clear the waiting message
                print(" " * 60, end='\r')
                
                # Get current market price
                market_price = self.get_current_market_price()
                
                # Generate signal
                try:
                    signal = self.signal_engine.generate_signal()
                    self.format_signal_output(signal, market_price)
                except Exception as e:
                    self.format_signal_output(None, market_price)
                
                # Wait a bit before next check
                time.sleep(2)
                
        except KeyboardInterrupt:
            print(f"\n⏹️ Signal generation stopped by user")
            print("✅ Bot shutdown complete")

if __name__ == "__main__":
    bot = SimpleFormattedBot()
    bot.run_signal_generation()
