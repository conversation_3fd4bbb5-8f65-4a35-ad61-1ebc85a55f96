#!/usr/bin/env python3
"""
Quick test script to verify the binary trading bot is working
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_bot():
    print("🚀 BINARY TRADING BOT VERIFICATION")
    print("=" * 50)
    
    # Test 1: OANDA Connection
    print("\n📡 Testing OANDA Connection...")
    try:
        from src.oanda_client import OandaClient
        client = OandaClient()
        print("✅ OANDA Client Created")
        
        # Test connection
        result = client.test_connection()
        print(f"✅ OANDA Connection: {'SUCCESS' if result else 'FAILED'}")
        
        if result:
            # Test data fetching
            data = client.get_candles(count=5)
            print(f"✅ Data Fetched: {len(data) if data is not None else 0} candles")
            
            # Test current price
            price = client.get_current_price()
            if price:
                print(f"✅ Current Price: {price['bid']:.5f} / {price['ask']:.5f}")
            else:
                print("❌ Could not fetch current price")
        
    except Exception as e:
        print(f"❌ OANDA Test Failed: {e}")
        return False
    
    # Test 2: Strategies
    print("\n🎯 Testing Trading Strategies...")
    try:
        from src.strategies.strategy1_breakout import BreakoutStrategy
        from src.strategies.strategy2_orderblock import OrderBlockStrategy
        from src.strategies.strategy3_pattern import PatternReversalStrategy
        
        strategies = {
            'Strategy 1 (Breakout)': BreakoutStrategy(),
            'Strategy 2 (Order Block)': OrderBlockStrategy(),
            'Strategy 3 (Pattern)': PatternReversalStrategy()
        }
        
        # Get test data
        data = client.get_candles(count=200)
        if data is None or len(data) < 100:
            print("❌ Insufficient data for strategy testing")
            return False
        
        for name, strategy in strategies.items():
            try:
                signal = strategy.analyze(data)
                if signal:
                    print(f"✅ {name}: SIGNAL GENERATED - {signal['direction']} ({signal['confidence']}%)")
                else:
                    print(f"✅ {name}: Working (no signal at this time)")
            except Exception as e:
                print(f"❌ {name}: Failed - {e}")
                return False
                
    except Exception as e:
        print(f"❌ Strategy Test Failed: {e}")
        return False
    
    # Test 3: Signal Engine
    print("\n⚡ Testing Signal Engine...")
    try:
        from src.signal_engine import SignalEngine
        engine = SignalEngine()
        print("✅ Signal Engine Created")
        
        # Test signal generation
        signal = engine.generate_signal()
        if signal:
            print(f"✅ Signal Generated: {signal['direction']} ({signal['confidence']}%) by {signal['strategy']}")
            print(f"   Price: {signal['current_price']:.5f}")
            print(f"   Time: {signal['timestamp']}")
        else:
            print("✅ Signal Engine Working (no signal generated at this time)")
            
    except Exception as e:
        print(f"❌ Signal Engine Test Failed: {e}")
        return False
    
    # Test 4: Backtesting
    print("\n📊 Testing Backtesting...")
    try:
        from src.backtesting import BacktestEngine
        backtest_engine = BacktestEngine()
        print("✅ Backtest Engine Created")
        
        # Load historical data
        data = backtest_engine.load_historical_data('historical_data_raw.csv')
        if not data.empty:
            print(f"✅ Historical Data Loaded: {len(data)} candles")
            
            # Quick backtest
            results = backtest_engine.run_backtest(
                data.iloc[-500:],  # Last 500 candles for quick test
                strategy_name='Strategy1_Breakout'
            )
            
            if 'error' not in results:
                perf = results.get('Strategy1_Breakout', {}).get('performance', {})
                print(f"✅ Backtest Completed: {perf.get('total_signals', 0)} signals, {perf.get('win_rate', 0)}% win rate")
            else:
                print(f"❌ Backtest Failed: {results['error']}")
        else:
            print("❌ Could not load historical data")
            
    except Exception as e:
        print(f"❌ Backtesting Test Failed: {e}")
        return False
    
    print("\n" + "=" * 50)
    print("🎉 ALL TESTS PASSED! Bot is ready for operation.")
    print("\nTo start the web interface:")
    print("python app.py")
    print("\nTo run signal generation:")
    print("python main.py signal")
    
    return True

if __name__ == "__main__":
    test_bot()
