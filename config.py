"""
Configuration settings for the Binary Trading Bot
"""
import os

class Config:
    # OANDA API Configuration
    OANDA_API_KEY = "69091c014738dc994e79ba405a77eb84-a46c00ab9195105c2f9a66331e92e9c1"
    OANDA_ACCOUNT_ID = "101-004-********-001"
    OANDA_BASE_URL = "https://api-fxpractice.oanda.com"  # Practice environment

    # Trading Configuration
    INSTRUMENT = "EUR_USD"  # Default trading pair
    TIMEFRAME = "M1"  # 1-minute candles
    SIGNAL_TIMING = 2  # Seconds before candle close to generate signal

    # Strategy Configuration
    LOOKBACK_PERIODS = 180  # 3 hours of 1-minute data
    VOLUME_THRESHOLD = 1.2  # Volume multiplier for confirmation

    # Strategy 3 Configuration
    MIN_MOVEMENT_PIPS = 5
    MAX_MOVEMENT_PIPS = 40
    PATTERN_LOOKBACK_HOURS = 3
    MIN_PATTERN_OCCURRENCES = 2

    # Database Configuration
    DATABASE_URL = "sqlite:///trading_bot.db"

    # Logging Configuration
    LOG_LEVEL = "INFO"
    LOG_FILE = "trading_bot.log"

    # Frontend Configuration
    FLASK_HOST = "127.0.0.1"
    FLASK_PORT = 5000
    FLASK_DEBUG = True

    # Backtesting Configuration
    BACKTEST_START_DATE = "2024-01-01"
    BACKTEST_END_DATE = "2024-12-31"

    @classmethod
    def get_oanda_headers(cls):
        """Get headers for OANDA API requests"""
        return {
            "Authorization": f"Bearer {cls.OANDA_API_KEY}",
            "Content-Type": "application/json"
        }
