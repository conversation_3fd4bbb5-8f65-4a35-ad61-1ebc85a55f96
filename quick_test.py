#!/usr/bin/env python3
"""
Quick test to verify all components are working
"""
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_price():
    print("🔍 Testing Current Price...")
    try:
        from src.oanda_client import OandaClient
        client = OandaClient()
        price = client.get_current_price()
        if price:
            print(f"✅ Current Price: {price['bid']:.5f} / {price['ask']:.5f}")
            return True
        else:
            print("❌ Could not fetch price")
            return False
    except Exception as e:
        print(f"❌ Price test failed: {e}")
        return False

def test_signal():
    print("\n🎯 Testing Signal Generation...")
    try:
        from src.signal_engine import SignalEngine
        engine = SignalEngine()
        signal = engine.generate_signal()
        if signal:
            print(f"✅ Signal Generated:")
            print(f"   Direction: {signal['direction']}")
            print(f"   Confidence: {signal['confidence']}%")
            print(f"   Strategy: {signal['strategy']}")
            print(f"   Price: {signal['current_price']:.5f}")
            return True
        else:
            print("✅ Signal engine working (no signal at this time)")
            return True
    except Exception as e:
        print(f"❌ Signal test failed: {e}")
        return False

def test_backtest():
    print("\n📊 Testing Quick Backtest...")
    try:
        from src.backtesting import BacktestEngine
        engine = BacktestEngine()
        data = engine.load_historical_data('historical_data_raw.csv')
        if data.empty:
            print("❌ Could not load historical data")
            return False
        
        print(f"✅ Loaded {len(data)} historical candles")
        
        # Quick backtest on last 100 candles
        results = engine.run_backtest(
            data.iloc[-100:],
            strategy_name='Strategy1_Breakout'
        )
        
        if 'error' in results:
            print(f"❌ Backtest failed: {results['error']}")
            return False
        
        perf = results.get('Strategy1_Breakout', {}).get('performance', {})
        print(f"✅ Backtest completed:")
        print(f"   Signals: {perf.get('total_signals', 0)}")
        print(f"   Win Rate: {perf.get('win_rate', 0)}%")
        return True
        
    except Exception as e:
        print(f"❌ Backtest failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 QUICK SYSTEM TEST")
    print("=" * 40)
    
    tests = [test_price, test_signal, test_backtest]
    passed = 0
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 40)
    print(f"📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All systems working! Website should be functional.")
        print("🌐 Open: http://127.0.0.1:8080")
    else:
        print("⚠️ Some issues detected. Check the errors above.")
