"""
Base Strategy Class for Binary Trading Bot
"""
from abc import ABC, abstractmethod
from typing import Dict, Optional, List
import pandas as pd
import numpy as np
import logging

class BaseStrategy(ABC):
    """
    Abstract base class for all trading strategies
    """

    def __init__(self, name: str):
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")

    @abstractmethod
    def analyze(self, data: pd.DataFrame) -> Optional[Dict]:
        """
        Analyze market data and generate signal

        Args:
            data: DataFrame with OHLCV data

        Returns:
            Signal dictionary or None if no signal
        """
        pass

    def calculate_support_resistance(self, data: pd.DataFrame, window: int = 20) -> Dict:
        """
        Calculate support and resistance levels

        Args:
            data: DataFrame with OHLCV data
            window: Lookback window for calculation

        Returns:
            Dictionary with support and resistance levels
        """
        if len(data) < window:
            return {'support': [], 'resistance': []}

        highs = data['high'].rolling(window=window, center=True).max()
        lows = data['low'].rolling(window=window, center=True).min()

        # Find local maxima (resistance) and minima (support)
        resistance_levels = []
        support_levels = []

        for i in range(window, len(data) - window):
            # Check if current high is a local maximum
            if data['high'].iloc[i] == highs.iloc[i]:
                # Count how many times price touched this level (relaxed)
                level = data['high'].iloc[i]
                touches = self._count_touches(data, level, tolerance=0.0002)  # Increased tolerance
                if touches >= 1:  # Relaxed from 2 to 1 touch
                    resistance_levels.append({
                        'level': level,
                        'touches': touches,
                        'index': i,
                        'time': data.index[i]
                    })

            # Check if current low is a local minimum
            if data['low'].iloc[i] == lows.iloc[i]:
                level = data['low'].iloc[i]
                touches = self._count_touches(data, level, tolerance=0.0002)  # Increased tolerance
                if touches >= 1:  # Relaxed from 2 to 1 touch
                    support_levels.append({
                        'level': level,
                        'touches': touches,
                        'index': i,
                        'time': data.index[i]
                    })

        return {
            'support': support_levels,
            'resistance': resistance_levels
        }

    def _count_touches(self, data: pd.DataFrame, level: float, tolerance: float = 0.0001) -> int:
        """
        Count how many times price touched a specific level

        Args:
            data: DataFrame with OHLCV data
            level: Price level to check
            tolerance: Tolerance for level matching

        Returns:
            Number of touches
        """
        touches = 0
        for _, row in data.iterrows():
            if (abs(row['high'] - level) <= tolerance or
                abs(row['low'] - level) <= tolerance or
                (row['low'] <= level <= row['high'])):
                touches += 1
        return touches

    def detect_order_blocks(self, data: pd.DataFrame, min_size: int = 3) -> List[Dict]:
        """
        Detect order blocks in the data

        Args:
            data: DataFrame with OHLCV data
            min_size: Minimum size of order block

        Returns:
            List of order block dictionaries
        """
        order_blocks = []

        for i in range(min_size, len(data) - min_size):
            # Look for bullish order block (strong buying pressure)
            if (data['close'].iloc[i] > data['open'].iloc[i] and  # Bullish candle
                data['volume'].iloc[i] > data['volume'].iloc[i-1] * 1.5):  # High volume

                # Check if followed by consolidation or pullback
                consolidation = True
                for j in range(i+1, min(i+min_size+1, len(data))):
                    if abs(data['close'].iloc[j] - data['close'].iloc[i]) > abs(data['close'].iloc[i] - data['open'].iloc[i]) * 2:
                        consolidation = False
                        break

                if consolidation:
                    order_blocks.append({
                        'type': 'bullish',
                        'start_index': i,
                        'high': data['high'].iloc[i],
                        'low': data['low'].iloc[i],
                        'volume': data['volume'].iloc[i],
                        'time': data.index[i]
                    })

            # Look for bearish order block (strong selling pressure)
            elif (data['close'].iloc[i] < data['open'].iloc[i] and  # Bearish candle
                  data['volume'].iloc[i] > data['volume'].iloc[i-1] * 1.5):  # High volume

                consolidation = True
                for j in range(i+1, min(i+min_size+1, len(data))):
                    if abs(data['close'].iloc[j] - data['close'].iloc[i]) > abs(data['open'].iloc[i] - data['close'].iloc[i]) * 2:
                        consolidation = False
                        break

                if consolidation:
                    order_blocks.append({
                        'type': 'bearish',
                        'start_index': i,
                        'high': data['high'].iloc[i],
                        'low': data['low'].iloc[i],
                        'volume': data['volume'].iloc[i],
                        'time': data.index[i]
                    })

        return order_blocks

    def calculate_pips_movement(self, price1: float, price2: float, instrument: str = "EUR_USD") -> float:
        """
        Calculate movement in pips between two prices

        Args:
            price1: Starting price
            price2: Ending price
            instrument: Trading instrument

        Returns:
            Movement in pips
        """
        # For most major pairs, 1 pip = 0.0001
        # For JPY pairs, 1 pip = 0.01
        if "JPY" in instrument:
            pip_value = 0.01
        else:
            pip_value = 0.0001

        return abs(price2 - price1) / pip_value

    def format_signal(self, direction: str, confidence: float, current_price: float,
                     strategy_name: str, details: Dict = None) -> Dict:
        """
        Format signal output

        Args:
            direction: 'UP' or 'DOWN'
            confidence: Confidence percentage (0-100)
            current_price: Current market price
            strategy_name: Name of strategy that generated signal
            details: Additional signal details

        Returns:
            Formatted signal dictionary
        """
        return {
            'timestamp': pd.Timestamp.now(),
            'direction': direction,
            'confidence': round(confidence, 2),
            'current_price': current_price,
            'strategy': strategy_name,
            'details': details or {}
        }
