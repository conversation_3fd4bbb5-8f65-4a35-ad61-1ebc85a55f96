#!/usr/bin/env python3
"""
Demo script to show the binary trading bot working
"""
import sys
import os
import time
from datetime import datetime
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def demo_live_signals():
    print("🚀 BINARY TRADING BOT - LIVE DEMO")
    print("=" * 60)
    
    from src.oanda_client import OandaClient
    from src.strategies.strategy1_breakout import BreakoutStrategy
    from src.strategies.strategy2_orderblock import OrderBlockStrategy
    from src.strategies.strategy3_pattern import PatternReversalStrategy
    
    # Initialize components
    client = OandaClient()
    strategies = {
        'Strategy1_Breakout': BreakoutStrategy(),
        'Strategy2_OrderBlock': OrderBlockStrategy(),
        'Strategy3_Pattern': PatternReversalStrategy()
    }
    
    print(f"📡 Connected to OANDA API")
    print(f"🎯 Loaded {len(strategies)} trading strategies")
    print(f"⏰ Starting live signal monitoring...")
    print("-" * 60)
    
    # Monitor for signals
    for i in range(5):  # Check 5 times
        print(f"\n🔍 Analysis #{i+1} - {datetime.now().strftime('%H:%M:%S')}")
        
        # Get live market data
        data = client.get_candles(count=200)
        current_price = client.get_current_price()
        
        if data is None or current_price is None:
            print("❌ Could not fetch market data")
            continue
        
        print(f"📊 Fetched {len(data)} candles")
        print(f"💰 Current Price: {current_price['bid']:.5f} / {current_price['ask']:.5f}")
        
        # Test each strategy
        signals_found = []
        
        for strategy_name, strategy in strategies.items():
            try:
                signal = strategy.analyze(data)
                if signal:
                    signal['current_price'] = (current_price['bid'] + current_price['ask']) / 2
                    signals_found.append(signal)
                    
                    print(f"🎯 {strategy_name}: {signal['direction']} signal ({signal['confidence']:.1f}% confidence)")
                    
                    # Show signal details
                    if 'details' in signal and signal['details']:
                        print(f"   📋 Details: {list(signal['details'].keys())}")
                else:
                    print(f"⚪ {strategy_name}: No signal")
                    
            except Exception as e:
                print(f"❌ {strategy_name}: Error - {e}")
        
        if signals_found:
            # Choose best signal
            best_signal = max(signals_found, key=lambda x: x['confidence'])
            print(f"\n🏆 BEST SIGNAL:")
            print(f"   Direction: {best_signal['direction']}")
            print(f"   Confidence: {best_signal['confidence']:.1f}%")
            print(f"   Strategy: {best_signal['strategy']}")
            print(f"   Price: {best_signal['current_price']:.5f}")
            print(f"   Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        else:
            print("⚪ No signals generated at this time")
        
        if i < 4:  # Don't wait after last iteration
            print("⏳ Waiting 10 seconds for next analysis...")
            time.sleep(10)
    
    print("\n" + "=" * 60)
    print("✅ Demo completed successfully!")
    print("\nThe bot is working and ready for live trading!")

def demo_backtesting():
    print("\n📊 BACKTESTING DEMO")
    print("-" * 40)
    
    from src.backtesting import BacktestEngine
    
    backtest_engine = BacktestEngine()
    
    # Load historical data
    print("📁 Loading historical data...")
    data = backtest_engine.load_historical_data('historical_data_raw.csv')
    
    if data.empty:
        print("❌ Could not load historical data")
        return
    
    print(f"✅ Loaded {len(data)} historical candles")
    
    # Run quick backtest on recent data
    print("🔄 Running backtest on last 1000 candles...")
    
    results = backtest_engine.run_backtest(
        data.iloc[-1000:],  # Last 1000 candles
        strategy_name='Strategy1_Breakout'
    )
    
    if 'error' in results:
        print(f"❌ Backtest failed: {results['error']}")
        return
    
    # Show results
    strategy_results = results.get('Strategy1_Breakout', {})
    performance = strategy_results.get('performance', {})
    
    print(f"\n📈 BACKTEST RESULTS:")
    print(f"   Total Signals: {performance.get('total_signals', 0)}")
    print(f"   Total Trades: {performance.get('total_trades', 0)}")
    print(f"   Win Rate: {performance.get('win_rate', 0):.1f}%")
    print(f"   Winning Trades: {performance.get('winning_trades', 0)}")
    print(f"   Losing Trades: {performance.get('losing_trades', 0)}")
    print(f"   Average Confidence: {performance.get('average_confidence', 0):.1f}%")
    
    if performance.get('direction_analysis'):
        dir_analysis = performance['direction_analysis']
        print(f"   UP Trades: {dir_analysis.get('up_trades', 0)} ({dir_analysis.get('up_win_rate', 0):.1f}% win rate)")
        print(f"   DOWN Trades: {dir_analysis.get('down_trades', 0)} ({dir_analysis.get('down_win_rate', 0):.1f}% win rate)")

if __name__ == "__main__":
    try:
        demo_live_signals()
        demo_backtesting()
    except KeyboardInterrupt:
        print("\n\n⏹️ Demo stopped by user")
    except Exception as e:
        print(f"\n❌ Demo error: {e}")
        import traceback
        traceback.print_exc()
